using System;
using System.Web.Mvc;
using MsWebGame.CSKH.Models;
using MsWebGame.CSKH.Database.DAO;
using MsWebGame.CSKH.App_Start;
using TraditionGame.Utilities;
using Telerik.Web.Mvc;
using System.Collections.Generic;
using System.Linq;

namespace MsWebGame.CSKH.Controllers
{
    [AllowedIP]
    public class WithdrawalSettingsController : BaseController
    {
        private readonly List<string> _acceptListAdmin = new List<string>() { "admin", "adminref", "admin_test", "cskh_01", "monitor_01" };

        /// <summary>
        /// Trang chính quản lý cài đặt rút tiền
        /// </summary>
        [AdminAuthorize(Roles = ADMIN_CALLCENTER_ROLE)]
        public ActionResult Index()
        {
            if (!_acceptListAdmin.Contains(AdminAccountName))
            {
                return RedirectToAction("Permission", "Account");
            }

            ViewBag.SettingTypes = GetSettingTypes();
            ViewBag.ApplyForOptions = GetApplyForOptions();
            
            return View();
        }

        /// <summary>
        /// L<PERSON>y danh sách cài đặt rút tiền (Ajax)
        /// </summary>
        [AdminAuthorize(Roles = ADMIN_CALLCENTER_ROLE)]
        [GridAction]
        public ActionResult GetWithdrawalSettings(WithdrawalSettingsSearchModel searchModel)
        {
            if (!_acceptListAdmin.Contains(AdminAccountName))
            {
                return Json(new { success = false, message = "Không có quyền truy cập" });
            }

            try
            {
                int totalRecord = 0;
                int currentPage = 1;
                int recordPerPage = 20;

                // Lấy thông tin phân trang từ request
                if (Request["page"] != null)
                    int.TryParse(Request["page"], out currentPage);
                if (Request["size"] != null)
                    int.TryParse(Request["size"], out recordPerPage);

                var list = WithdrawalSettingsDAO.Instance.GetWithdrawalSettings(searchModel, currentPage, recordPerPage, out totalRecord);

                return View(new GridModel<WithdrawalSettingsDTO>
                {
                    Data = list,
                    Total = totalRecord
                });
            }
            catch (Exception ex)
            {
                NLogManager.PublishException(ex);
                return Json(new { success = false, message = "Có lỗi xảy ra: " + ex.Message });
            }
        }

        /// <summary>
        /// Trang thêm mới cài đặt rút tiền
        /// </summary>
        [AdminAuthorize(Roles = ADMIN_CALLCENTER_ROLE)]
        public ActionResult Create()
        {
            if (!_acceptListAdmin.Contains(AdminAccountName))
            {
                return RedirectToAction("Permission", "Account");
            }

            ViewBag.SettingTypes = GetSettingTypes();
            ViewBag.ApplyForOptions = GetApplyForOptions();

            var model = new WithdrawalSettingsModel();
            return View(model);
        }

        /// <summary>
        /// Xử lý thêm mới cài đặt rút tiền
        /// </summary>
        [HttpPost]
        [AdminAuthorize(Roles = ADMIN_CALLCENTER_ROLE)]
        public ActionResult Create(WithdrawalSettingsModel model)
        {
            if (!_acceptListAdmin.Contains(AdminAccountName))
            {
                return RedirectToAction("Permission", "Account");
            }

            ViewBag.SettingTypes = GetSettingTypes();
            ViewBag.ApplyForOptions = GetApplyForOptions();

            try
            {
                if (ModelState.IsValid)
                {
                    // Validate business rules
                    if (model.MinAmount >= model.MaxAmount)
                    {
                        ViewBag.Message = "Giá trị tối thiểu phải nhỏ hơn giá trị tối đa";
                        return View(model);
                    }

                    int responseStatus;
                    int newId = WithdrawalSettingsDAO.Instance.CreateWithdrawalSetting(model, AdminAccountName, out responseStatus);

                    if (responseStatus == 1)
                    {
                        ViewBag.Message = "Thêm mới cài đặt rút tiền thành công";
                        ViewBag.MessageType = "success";
                        
                        // Log activity
                        NLogManager.LogMessage($"Admin {AdminAccountName} đã tạo cài đặt rút tiền mới: {model.SettingName}");
                        
                        return View(new WithdrawalSettingsModel()); // Reset form
                    }
                    else if (responseStatus == -1)
                    {
                        ViewBag.Message = "Tên cài đặt đã tồn tại";
                    }
                    else if (responseStatus == -2)
                    {
                        ViewBag.Message = "Loại cài đặt và áp dụng cho đã tồn tại";
                    }
                    else
                    {
                        ViewBag.Message = "Có lỗi xảy ra khi thêm mới cài đặt. Mã lỗi: " + responseStatus;
                    }
                }
                else
                {
                    ViewBag.Message = "Vui lòng kiểm tra lại thông tin nhập vào";
                }
            }
            catch (Exception ex)
            {
                NLogManager.PublishException(ex);
                ViewBag.Message = "Có lỗi xảy ra: " + ex.Message;
            }

            return View(model);
        }

        /// <summary>
        /// Trang chỉnh sửa cài đặt rút tiền
        /// </summary>
        [AdminAuthorize(Roles = ADMIN_CALLCENTER_ROLE)]
        public ActionResult Edit(int id)
        {
            if (!_acceptListAdmin.Contains(AdminAccountName))
            {
                return RedirectToAction("Permission", "Account");
            }

            ViewBag.SettingTypes = GetSettingTypes();
            ViewBag.ApplyForOptions = GetApplyForOptions();

            try
            {
                var setting = WithdrawalSettingsDAO.Instance.GetWithdrawalSettingById(id);
                if (setting == null)
                {
                    ViewBag.Message = "Không tìm thấy cài đặt rút tiền";
                    return RedirectToAction("Index");
                }

                var model = new WithdrawalSettingsModel
                {
                    ID = setting.ID,
                    SettingName = setting.SettingName,
                    Description = setting.Description,
                    MinAmount = setting.MinAmount,
                    MaxAmount = setting.MaxAmount,
                    IsActive = setting.IsActive,
                    Note = setting.Note,
                    SettingType = setting.SettingType,
                    ApplyFor = setting.ApplyFor
                };

                return View(model);
            }
            catch (Exception ex)
            {
                NLogManager.PublishException(ex);
                ViewBag.Message = "Có lỗi xảy ra: " + ex.Message;
                return RedirectToAction("Index");
            }
        }

        /// <summary>
        /// Xử lý chỉnh sửa cài đặt rút tiền
        /// </summary>
        [HttpPost]
        [AdminAuthorize(Roles = ADMIN_CALLCENTER_ROLE)]
        public ActionResult Edit(WithdrawalSettingsModel model)
        {
            if (!_acceptListAdmin.Contains(AdminAccountName))
            {
                return RedirectToAction("Permission", "Account");
            }

            ViewBag.SettingTypes = GetSettingTypes();
            ViewBag.ApplyForOptions = GetApplyForOptions();

            try
            {
                if (ModelState.IsValid)
                {
                    // Validate business rules
                    if (model.MinAmount >= model.MaxAmount)
                    {
                        ViewBag.Message = "Giá trị tối thiểu phải nhỏ hơn giá trị tối đa";
                        return View(model);
                    }

                    int responseStatus;
                    WithdrawalSettingsDAO.Instance.UpdateWithdrawalSetting(model, AdminAccountName, out responseStatus);

                    if (responseStatus == 1)
                    {
                        ViewBag.Message = "Cập nhật cài đặt rút tiền thành công";
                        ViewBag.MessageType = "success";
                        
                        // Log activity
                        NLogManager.LogMessage($"Admin {AdminAccountName} đã cập nhật cài đặt rút tiền: {model.SettingName}");
                    }
                    else if (responseStatus == -1)
                    {
                        ViewBag.Message = "Tên cài đặt đã tồn tại";
                    }
                    else if (responseStatus == -2)
                    {
                        ViewBag.Message = "Không tìm thấy cài đặt rút tiền";
                    }
                    else
                    {
                        ViewBag.Message = "Có lỗi xảy ra khi cập nhật cài đặt. Mã lỗi: " + responseStatus;
                    }
                }
                else
                {
                    ViewBag.Message = "Vui lòng kiểm tra lại thông tin nhập vào";
                }
            }
            catch (Exception ex)
            {
                NLogManager.PublishException(ex);
                ViewBag.Message = "Có lỗi xảy ra: " + ex.Message;
            }

            return View(model);
        }

        /// <summary>
        /// Xóa cài đặt rút tiền
        /// </summary>
        [HttpPost]
        [AdminAuthorize(Roles = ADMIN_CALLCENTER_ROLE)]
        public JsonResult Delete(int id)
        {
            if (!_acceptListAdmin.Contains(AdminAccountName))
            {
                return Json(new { success = false, message = "Không có quyền truy cập" });
            }

            try
            {
                int responseStatus;
                WithdrawalSettingsDAO.Instance.DeleteWithdrawalSetting(id, AdminAccountName, out responseStatus);

                if (responseStatus == 1)
                {
                    // Log activity
                    NLogManager.LogMessage($"Admin {AdminAccountName} đã xóa cài đặt rút tiền ID: {id}");
                    
                    return Json(new { success = true, message = "Xóa cài đặt rút tiền thành công" });
                }
                else if (responseStatus == -2)
                {
                    return Json(new { success = false, message = "Không tìm thấy cài đặt rút tiền" });
                }
                else
                {
                    return Json(new { success = false, message = "Có lỗi xảy ra khi xóa cài đặt. Mã lỗi: " + responseStatus });
                }
            }
            catch (Exception ex)
            {
                NLogManager.PublishException(ex);
                return Json(new { success = false, message = "Có lỗi xảy ra: " + ex.Message });
            }
        }

        #region Private Methods

        /// <summary>
        /// Lấy danh sách loại cài đặt
        /// </summary>
        private List<SelectListItem> GetSettingTypes()
        {
            return new List<SelectListItem>
            {
                new SelectListItem { Value = "", Text = "-- Chọn loại cài đặt --" },
                new SelectListItem { Value = "WITHDRAWAL_LIMIT", Text = "Hạn mức rút tiền" },
                new SelectListItem { Value = "DAILY_LIMIT", Text = "Hạn mức rút tiền hàng ngày" },
                new SelectListItem { Value = "MONTHLY_LIMIT", Text = "Hạn mức rút tiền hàng tháng" }
            };
        }

        /// <summary>
        /// Lấy danh sách áp dụng cho
        /// </summary>
        private List<SelectListItem> GetApplyForOptions()
        {
            return new List<SelectListItem>
            {
                new SelectListItem { Value = "", Text = "-- Chọn áp dụng cho --" },
                new SelectListItem { Value = "ALL", Text = "Tất cả" },
                new SelectListItem { Value = "VIP", Text = "Thành viên VIP" },
                new SelectListItem { Value = "NORMAL", Text = "Thành viên thường" }
            };
        }

        #endregion
    }
}
