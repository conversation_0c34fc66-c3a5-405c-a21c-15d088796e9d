using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using MsWebGame.CSKH.Models;
using TraditionGame.Utilities;

namespace MsWebGame.CSKH.Database.DAO
{
    /// <summary>
    /// Data Access Object cho cài đặt rút tiền
    /// </summary>
    public class WithdrawalSettingsDAO
    {
        private static WithdrawalSettingsDAO _instance;
        private static readonly object _lock = new object();

        public static WithdrawalSettingsDAO Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                            _instance = new WithdrawalSettingsDAO();
                    }
                }
                return _instance;
            }
        }

        private WithdrawalSettingsDAO() { }

        /// <summary>
        /// L<PERSON>y danh sách cài đặt rút tiền
        /// </summary>
        public List<WithdrawalSettingsDTO> GetWithdrawalSettings(WithdrawalSettingsSearchModel searchModel, int currentPage, int recordPerpage, out int totalRecord)
        {
            totalRecord = 0;
            List<WithdrawalSettingsDTO> list = new List<WithdrawalSettingsDTO>();

            try
            {
                using (var db = new SqlConnection(AppConstants.DBS.ADMIN))
                {
                    db.Open();
                    using (var cmd = new SqlCommand("SP_WithdrawalSettings_GetList", db))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@SettingType", searchModel.SettingType ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@SettingName", searchModel.SettingName ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@IsActive", searchModel.IsActive ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ApplyFor", searchModel.ApplyFor ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@CurrentPage", currentPage);
                        cmd.Parameters.AddWithValue("@RecordPerPage", recordPerpage);

                        var totalRecordParam = new SqlParameter("@TotalRecord", SqlDbType.Int) { Direction = ParameterDirection.Output };
                        cmd.Parameters.Add(totalRecordParam);

                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                list.Add(new WithdrawalSettingsDTO
                                {
                                    ID = Convert.ToInt32(reader["ID"]),
                                    SettingName = reader["SettingName"].ToString(),
                                    Description = reader["Description"].ToString(),
                                    MinAmount = Convert.ToInt64(reader["MinAmount"]),
                                    MaxAmount = Convert.ToInt64(reader["MaxAmount"]),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                    UpdatedDate = reader["UpdatedDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["UpdatedDate"]),
                                    CreatedBy = reader["CreatedBy"].ToString(),
                                    UpdatedBy = reader["UpdatedBy"].ToString(),
                                    Note = reader["Note"].ToString(),
                                    SettingType = reader["SettingType"].ToString(),
                                    ApplyFor = reader["ApplyFor"].ToString()
                                });
                            }
                        }

                        totalRecord = Convert.ToInt32(totalRecordParam.Value);
                    }
                }
            }
            catch (Exception ex)
            {
                NLogManager.PublishException(ex);
            }

            return list;
        }

        /// <summary>
        /// Lấy cài đặt rút tiền theo ID
        /// </summary>
        public WithdrawalSettingsDTO GetWithdrawalSettingById(int id)
        {
            WithdrawalSettingsDTO setting = null;

            try
            {
                using (var db = new SqlConnection(AppConstants.DBS.ADMIN))
                {
                    db.Open();
                    using (var cmd = new SqlCommand("SP_WithdrawalSettings_GetById", db))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@ID", id);

                        using (var reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                setting = new WithdrawalSettingsDTO
                                {
                                    ID = Convert.ToInt32(reader["ID"]),
                                    SettingName = reader["SettingName"].ToString(),
                                    Description = reader["Description"].ToString(),
                                    MinAmount = Convert.ToInt64(reader["MinAmount"]),
                                    MaxAmount = Convert.ToInt64(reader["MaxAmount"]),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                    UpdatedDate = reader["UpdatedDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["UpdatedDate"]),
                                    CreatedBy = reader["CreatedBy"].ToString(),
                                    UpdatedBy = reader["UpdatedBy"].ToString(),
                                    Note = reader["Note"].ToString(),
                                    SettingType = reader["SettingType"].ToString(),
                                    ApplyFor = reader["ApplyFor"].ToString()
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                NLogManager.PublishException(ex);
            }

            return setting;
        }

        /// <summary>
        /// Thêm mới cài đặt rút tiền
        /// </summary>
        public int CreateWithdrawalSetting(WithdrawalSettingsModel model, string createdBy, out int responseStatus)
        {
            responseStatus = -99;
            int newId = 0;

            try
            {
                using (var db = new SqlConnection(AppConstants.DBS.ADMIN))
                {
                    db.Open();
                    using (var cmd = new SqlCommand("SP_WithdrawalSettings_Create", db))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@SettingName", model.SettingName);
                        cmd.Parameters.AddWithValue("@Description", model.Description);
                        cmd.Parameters.AddWithValue("@MinAmount", model.MinAmount);
                        cmd.Parameters.AddWithValue("@MaxAmount", model.MaxAmount);
                        cmd.Parameters.AddWithValue("@IsActive", model.IsActive);
                        cmd.Parameters.AddWithValue("@CreatedBy", createdBy);
                        cmd.Parameters.AddWithValue("@Note", model.Note ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@SettingType", model.SettingType);
                        cmd.Parameters.AddWithValue("@ApplyFor", model.ApplyFor);

                        var responseParam = new SqlParameter("@ResponseStatus", SqlDbType.Int) { Direction = ParameterDirection.Output };
                        cmd.Parameters.Add(responseParam);

                        var newIdParam = new SqlParameter("@NewID", SqlDbType.Int) { Direction = ParameterDirection.Output };
                        cmd.Parameters.Add(newIdParam);

                        cmd.ExecuteNonQuery();

                        responseStatus = Convert.ToInt32(responseParam.Value);
                        newId = Convert.ToInt32(newIdParam.Value);
                    }
                }
            }
            catch (Exception ex)
            {
                NLogManager.PublishException(ex);
                responseStatus = -99;
            }

            return newId;
        }

        /// <summary>
        /// Cập nhật cài đặt rút tiền
        /// </summary>
        public int UpdateWithdrawalSetting(WithdrawalSettingsModel model, string updatedBy, out int responseStatus)
        {
            responseStatus = -99;

            try
            {
                using (var db = new SqlConnection(AppConstants.DBS.ADMIN))
                {
                    db.Open();
                    using (var cmd = new SqlCommand("SP_WithdrawalSettings_Update", db))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@ID", model.ID);
                        cmd.Parameters.AddWithValue("@SettingName", model.SettingName);
                        cmd.Parameters.AddWithValue("@Description", model.Description);
                        cmd.Parameters.AddWithValue("@MinAmount", model.MinAmount);
                        cmd.Parameters.AddWithValue("@MaxAmount", model.MaxAmount);
                        cmd.Parameters.AddWithValue("@IsActive", model.IsActive);
                        cmd.Parameters.AddWithValue("@UpdatedBy", updatedBy);
                        cmd.Parameters.AddWithValue("@Note", model.Note ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@SettingType", model.SettingType);
                        cmd.Parameters.AddWithValue("@ApplyFor", model.ApplyFor);

                        var responseParam = new SqlParameter("@ResponseStatus", SqlDbType.Int) { Direction = ParameterDirection.Output };
                        cmd.Parameters.Add(responseParam);

                        cmd.ExecuteNonQuery();

                        responseStatus = Convert.ToInt32(responseParam.Value);
                    }
                }
            }
            catch (Exception ex)
            {
                NLogManager.PublishException(ex);
                responseStatus = -99;
            }

            return responseStatus;
        }

        /// <summary>
        /// Xóa cài đặt rút tiền
        /// </summary>
        public int DeleteWithdrawalSetting(int id, string deletedBy, out int responseStatus)
        {
            responseStatus = -99;

            try
            {
                using (var db = new SqlConnection(AppConstants.DBS.ADMIN))
                {
                    db.Open();
                    using (var cmd = new SqlCommand("SP_WithdrawalSettings_Delete", db))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@ID", id);
                        cmd.Parameters.AddWithValue("@DeletedBy", deletedBy);

                        var responseParam = new SqlParameter("@ResponseStatus", SqlDbType.Int) { Direction = ParameterDirection.Output };
                        cmd.Parameters.Add(responseParam);

                        cmd.ExecuteNonQuery();

                        responseStatus = Convert.ToInt32(responseParam.Value);
                    }
                }
            }
            catch (Exception ex)
            {
                NLogManager.PublishException(ex);
                responseStatus = -99;
            }

            return responseStatus;
        }

        /// <summary>
        /// Lấy cài đặt rút tiền theo loại và áp dụng cho
        /// </summary>
        public WithdrawalSettingsDTO GetWithdrawalSettingByTypeAndApplyFor(string settingType, string applyFor)
        {
            WithdrawalSettingsDTO setting = null;

            try
            {
                using (var db = new SqlConnection(AppConstants.DBS.ADMIN))
                {
                    db.Open();
                    using (var cmd = new SqlCommand("SP_WithdrawalSettings_GetByTypeAndApplyFor", db))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@SettingType", settingType);
                        cmd.Parameters.AddWithValue("@ApplyFor", applyFor);

                        using (var reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                setting = new WithdrawalSettingsDTO
                                {
                                    ID = Convert.ToInt32(reader["ID"]),
                                    SettingName = reader["SettingName"].ToString(),
                                    Description = reader["Description"].ToString(),
                                    MinAmount = Convert.ToInt64(reader["MinAmount"]),
                                    MaxAmount = Convert.ToInt64(reader["MaxAmount"]),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                    UpdatedDate = reader["UpdatedDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["UpdatedDate"]),
                                    CreatedBy = reader["CreatedBy"].ToString(),
                                    UpdatedBy = reader["UpdatedBy"].ToString(),
                                    Note = reader["Note"].ToString(),
                                    SettingType = reader["SettingType"].ToString(),
                                    ApplyFor = reader["ApplyFor"].ToString()
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                NLogManager.PublishException(ex);
            }

            return setting;
        }
    }
}
