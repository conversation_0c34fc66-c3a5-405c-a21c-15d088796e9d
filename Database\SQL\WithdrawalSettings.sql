-- =============================================
-- Script tạo bảng và stored procedures cho WithdrawalSettings
-- Author: Admin System
-- Created: 2025
-- =============================================

-- Tạo bảng WithdrawalSettings
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='WithdrawalSettings' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[WithdrawalSettings](
        [ID] [int] IDENTITY(1,1) NOT NULL,
        [SettingName] [nvarchar](100) NOT NULL,
        [Description] [nvarchar](500) NOT NULL,
        [MinAmount] [bigint] NOT NULL DEFAULT(0),
        [MaxAmount] [bigint] NOT NULL DEFAULT(0),
        [IsActive] [bit] NOT NULL DEFAULT(1),
        [CreatedDate] [datetime] NOT NULL DEFAULT(GETDATE()),
        [UpdatedDate] [datetime] NULL,
        [CreatedBy] [nvarchar](50) NOT NULL,
        [UpdatedBy] [nvarchar](50) NULL,
        [Note] [nvarchar](1000) NULL,
        [SettingType] [nvarchar](50) NOT NULL,
        [ApplyFor] [nvarchar](20) NOT NULL DEFAULT('ALL'),
        CONSTRAINT [PK_WithdrawalSettings] PRIMARY KEY CLUSTERED ([ID] ASC)
    )
    
    -- Tạo index
    CREATE NONCLUSTERED INDEX [IX_WithdrawalSettings_SettingType] ON [dbo].[WithdrawalSettings] ([SettingType])
    CREATE NONCLUSTERED INDEX [IX_WithdrawalSettings_ApplyFor] ON [dbo].[WithdrawalSettings] ([ApplyFor])
    CREATE NONCLUSTERED INDEX [IX_WithdrawalSettings_IsActive] ON [dbo].[WithdrawalSettings] ([IsActive])
    CREATE UNIQUE NONCLUSTERED INDEX [IX_WithdrawalSettings_Unique] ON [dbo].[WithdrawalSettings] ([SettingType], [ApplyFor]) WHERE [IsActive] = 1
    
    PRINT 'Tạo bảng WithdrawalSettings thành công'
END
ELSE
BEGIN
    PRINT 'Bảng WithdrawalSettings đã tồn tại'
END
GO

-- =============================================
-- Stored Procedure: Lấy danh sách cài đặt rút tiền
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SP_WithdrawalSettings_GetList]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[SP_WithdrawalSettings_GetList]
GO

CREATE PROCEDURE [dbo].[SP_WithdrawalSettings_GetList]
    @SettingType NVARCHAR(50) = NULL,
    @SettingName NVARCHAR(100) = NULL,
    @IsActive BIT = NULL,
    @ApplyFor NVARCHAR(20) = NULL,
    @CurrentPage INT = 1,
    @RecordPerPage INT = 20,
    @TotalRecord INT OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartRow INT, @EndRow INT
    SET @StartRow = (@CurrentPage - 1) * @RecordPerPage + 1
    SET @EndRow = @CurrentPage * @RecordPerPage
    
    -- Đếm tổng số bản ghi
    SELECT @TotalRecord = COUNT(*)
    FROM WithdrawalSettings
    WHERE (@SettingType IS NULL OR SettingType = @SettingType)
        AND (@SettingName IS NULL OR SettingName LIKE '%' + @SettingName + '%')
        AND (@IsActive IS NULL OR IsActive = @IsActive)
        AND (@ApplyFor IS NULL OR ApplyFor = @ApplyFor)
    
    -- Lấy dữ liệu phân trang
    SELECT *
    FROM (
        SELECT ROW_NUMBER() OVER (ORDER BY CreatedDate DESC) AS RowNum,
               ID, SettingName, Description, MinAmount, MaxAmount, IsActive,
               CreatedDate, UpdatedDate, CreatedBy, UpdatedBy, Note,
               SettingType, ApplyFor
        FROM WithdrawalSettings
        WHERE (@SettingType IS NULL OR SettingType = @SettingType)
            AND (@SettingName IS NULL OR SettingName LIKE '%' + @SettingName + '%')
            AND (@IsActive IS NULL OR IsActive = @IsActive)
            AND (@ApplyFor IS NULL OR ApplyFor = @ApplyFor)
    ) AS T
    WHERE T.RowNum BETWEEN @StartRow AND @EndRow
    ORDER BY T.RowNum
END
GO

-- =============================================
-- Stored Procedure: Lấy cài đặt theo ID
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SP_WithdrawalSettings_GetById]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[SP_WithdrawalSettings_GetById]
GO

CREATE PROCEDURE [dbo].[SP_WithdrawalSettings_GetById]
    @ID INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT ID, SettingName, Description, MinAmount, MaxAmount, IsActive,
           CreatedDate, UpdatedDate, CreatedBy, UpdatedBy, Note,
           SettingType, ApplyFor
    FROM WithdrawalSettings
    WHERE ID = @ID
END
GO

-- =============================================
-- Stored Procedure: Thêm mới cài đặt
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SP_WithdrawalSettings_Create]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[SP_WithdrawalSettings_Create]
GO

CREATE PROCEDURE [dbo].[SP_WithdrawalSettings_Create]
    @SettingName NVARCHAR(100),
    @Description NVARCHAR(500),
    @MinAmount BIGINT,
    @MaxAmount BIGINT,
    @IsActive BIT,
    @CreatedBy NVARCHAR(50),
    @Note NVARCHAR(1000) = NULL,
    @SettingType NVARCHAR(50),
    @ApplyFor NVARCHAR(20),
    @ResponseStatus INT OUTPUT,
    @NewID INT OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    SET @ResponseStatus = -99
    SET @NewID = 0
    
    BEGIN TRY
        -- Kiểm tra tên cài đặt đã tồn tại
        IF EXISTS (SELECT 1 FROM WithdrawalSettings WHERE SettingName = @SettingName)
        BEGIN
            SET @ResponseStatus = -1 -- Tên cài đặt đã tồn tại
            RETURN
        END
        
        -- Kiểm tra loại cài đặt và áp dụng cho đã tồn tại
        IF EXISTS (SELECT 1 FROM WithdrawalSettings WHERE SettingType = @SettingType AND ApplyFor = @ApplyFor AND IsActive = 1)
        BEGIN
            SET @ResponseStatus = -2 -- Loại cài đặt và áp dụng cho đã tồn tại
            RETURN
        END
        
        -- Thêm mới
        INSERT INTO WithdrawalSettings (
            SettingName, Description, MinAmount, MaxAmount, IsActive,
            CreatedBy, Note, SettingType, ApplyFor
        )
        VALUES (
            @SettingName, @Description, @MinAmount, @MaxAmount, @IsActive,
            @CreatedBy, @Note, @SettingType, @ApplyFor
        )
        
        SET @NewID = SCOPE_IDENTITY()
        SET @ResponseStatus = 1 -- Thành công
        
    END TRY
    BEGIN CATCH
        SET @ResponseStatus = -99 -- Lỗi hệ thống
    END CATCH
END
GO

-- =============================================
-- Stored Procedure: Cập nhật cài đặt
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SP_WithdrawalSettings_Update]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[SP_WithdrawalSettings_Update]
GO

CREATE PROCEDURE [dbo].[SP_WithdrawalSettings_Update]
    @ID INT,
    @SettingName NVARCHAR(100),
    @Description NVARCHAR(500),
    @MinAmount BIGINT,
    @MaxAmount BIGINT,
    @IsActive BIT,
    @UpdatedBy NVARCHAR(50),
    @Note NVARCHAR(1000) = NULL,
    @SettingType NVARCHAR(50),
    @ApplyFor NVARCHAR(20),
    @ResponseStatus INT OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    SET @ResponseStatus = -99
    
    BEGIN TRY
        -- Kiểm tra cài đặt có tồn tại
        IF NOT EXISTS (SELECT 1 FROM WithdrawalSettings WHERE ID = @ID)
        BEGIN
            SET @ResponseStatus = -2 -- Không tìm thấy cài đặt
            RETURN
        END
        
        -- Kiểm tra tên cài đặt đã tồn tại (trừ chính nó)
        IF EXISTS (SELECT 1 FROM WithdrawalSettings WHERE SettingName = @SettingName AND ID != @ID)
        BEGIN
            SET @ResponseStatus = -1 -- Tên cài đặt đã tồn tại
            RETURN
        END
        
        -- Cập nhật
        UPDATE WithdrawalSettings
        SET SettingName = @SettingName,
            Description = @Description,
            MinAmount = @MinAmount,
            MaxAmount = @MaxAmount,
            IsActive = @IsActive,
            UpdatedBy = @UpdatedBy,
            UpdatedDate = GETDATE(),
            Note = @Note,
            SettingType = @SettingType,
            ApplyFor = @ApplyFor
        WHERE ID = @ID
        
        SET @ResponseStatus = 1 -- Thành công
        
    END TRY
    BEGIN CATCH
        SET @ResponseStatus = -99 -- Lỗi hệ thống
    END CATCH
END
GO

-- =============================================
-- Stored Procedure: Xóa cài đặt
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SP_WithdrawalSettings_Delete]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[SP_WithdrawalSettings_Delete]
GO

CREATE PROCEDURE [dbo].[SP_WithdrawalSettings_Delete]
    @ID INT,
    @DeletedBy NVARCHAR(50),
    @ResponseStatus INT OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    SET @ResponseStatus = -99
    
    BEGIN TRY
        -- Kiểm tra cài đặt có tồn tại
        IF NOT EXISTS (SELECT 1 FROM WithdrawalSettings WHERE ID = @ID)
        BEGIN
            SET @ResponseStatus = -2 -- Không tìm thấy cài đặt
            RETURN
        END
        
        -- Xóa cài đặt (soft delete - chuyển IsActive = 0)
        UPDATE WithdrawalSettings
        SET IsActive = 0,
            UpdatedBy = @DeletedBy,
            UpdatedDate = GETDATE()
        WHERE ID = @ID
        
        SET @ResponseStatus = 1 -- Thành công
        
    END TRY
    BEGIN CATCH
        SET @ResponseStatus = -99 -- Lỗi hệ thống
    END CATCH
END
GO

-- =============================================
-- Stored Procedure: Lấy cài đặt theo loại và áp dụng cho
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SP_WithdrawalSettings_GetByTypeAndApplyFor]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[SP_WithdrawalSettings_GetByTypeAndApplyFor]
GO

CREATE PROCEDURE [dbo].[SP_WithdrawalSettings_GetByTypeAndApplyFor]
    @SettingType NVARCHAR(50),
    @ApplyFor NVARCHAR(20)
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT TOP 1 ID, SettingName, Description, MinAmount, MaxAmount, IsActive,
           CreatedDate, UpdatedDate, CreatedBy, UpdatedBy, Note,
           SettingType, ApplyFor
    FROM WithdrawalSettings
    WHERE SettingType = @SettingType 
        AND ApplyFor = @ApplyFor 
        AND IsActive = 1
    ORDER BY CreatedDate DESC
END
GO

-- Thêm dữ liệu mẫu
INSERT INTO WithdrawalSettings (SettingName, Description, MinAmount, MaxAmount, SettingType, ApplyFor, CreatedBy)
VALUES 
    (N'Hạn mức rút tiền cơ bản', N'Hạn mức rút tiền áp dụng cho tất cả thành viên', 50000, 10000000, 'WITHDRAWAL_LIMIT', 'ALL', 'admin'),
    (N'Hạn mức rút tiền VIP', N'Hạn mức rút tiền áp dụng cho thành viên VIP', 100000, 50000000, 'WITHDRAWAL_LIMIT', 'VIP', 'admin'),
    (N'Hạn mức rút tiền hàng ngày', N'Hạn mức rút tiền tối đa trong 1 ngày', 100000, 5000000, 'DAILY_LIMIT', 'ALL', 'admin')

PRINT 'Tạo stored procedures và dữ liệu mẫu thành công'
