# Hướng dẫn Cài đặt Chức năng MIN/MAX Rút tiền

## Bước 1: Cài đặt Database

### 1.1 Ch<PERSON>y Script SQL
```sql
-- Mở SQL Server Management Studio
-- Kết nối đến database của dự án
-- Mở file Database/SQL/WithdrawalSettings.sql
-- <PERSON><PERSON><PERSON> toàn bộ script
```

### 1.2 <PERSON><PERSON><PERSON> tra kết quả
```sql
-- <PERSON><PERSON><PERSON> tra bảng đã được tạo
SELECT * FROM WithdrawalSettings

-- Ki<PERSON><PERSON> tra stored procedures
SELECT name FROM sys.procedures WHERE name LIKE '%WithdrawalSettings%'
```

## Bước 2: Cấu hình Connection String

Đảm bảo connection string `AppConstants.DBS.ADMIN` đã được cấu hình đúng trong:
- `web.config` hoặc
- `AppConstants.cs`

## Bước 3: Thêm vào Menu Navigation

### 3.1 Cách 1: Sử dụng Partial View
Trong file layout chính (thường là `_Layout.cshtml`), thêm:
```html
@Html.Partial("_WithdrawalSettingsMenu")
```

### 3.2 Cách 2: Thêm trực tiếp vào menu
```html
<li>
    <a href="/WithdrawalSettings">
        <img src="~/Content/images/ico-configuration.png" alt="" />
        Cài đặt rút tiền
    </a>
</li>
```

## Bước 4: Cấu hình Route (Tùy chọn)

Nếu cần custom route, thêm vào `RouteConfig.cs`:
```csharp
routes.MapRoute(
    name: "WithdrawalSettings",
    url: "admin/withdrawal-settings/{action}/{id}",
    defaults: new { controller = "WithdrawalSettings", action = "Index", id = UrlParameter.Optional },
    namespaces: new[] { "MsWebGame.CSKH.Controllers" }
);
```

## Bước 5: Cấu hình Quyền Truy cập

### 5.1 Cập nhật danh sách admin được phép
Trong `WithdrawalSettingsController.cs`, cập nhật:
```csharp
private readonly List<string> _acceptListAdmin = new List<string>() 
{ 
    "admin", "adminref", "admin_test", "cskh_01", "monitor_01",
    // Thêm tài khoản admin khác nếu cần
};
```

### 5.2 Cấu hình Role (nếu cần)
Cập nhật `ADMIN_CALLCENTER_ROLE` trong `BaseController` nếu cần thay đổi role.

## Bước 6: Test Chức năng

### 6.1 Truy cập trang chính
- URL: `http://yoursite.com/WithdrawalSettings`
- Đăng nhập với tài khoản admin có quyền

### 6.2 Test các chức năng
1. **Xem danh sách**: Kiểm tra hiển thị dữ liệu mẫu
2. **Tìm kiếm**: Test các filter
3. **Thêm mới**: Tạo cài đặt mới
4. **Chỉnh sửa**: Sửa cài đặt hiện có
5. **Xóa**: Test soft delete

### 6.3 Test Validation
- Nhập MIN >= MAX (phải báo lỗi)
- Tên cài đặt trùng (phải báo lỗi)
- Bỏ trống required fields (phải báo lỗi)

## Bước 7: Tích hợp với Logic Rút tiền

### 7.1 Thêm vào Controller rút tiền
```csharp
public class WithdrawalController : BaseController
{
    public ActionResult ProcessWithdrawal(long amount, long userId)
    {
        // Lấy thông tin user
        var user = UserDAO.Instance.GetById(userId);
        
        // Xác định loại user
        string userType = user.IsVIP ? "VIP" : "NORMAL";
        
        // Lấy cài đặt rút tiền
        var setting = WithdrawalSettingsDAO.Instance
            .GetWithdrawalSettingByTypeAndApplyFor("WITHDRAWAL_LIMIT", userType);
            
        if (setting == null)
        {
            // Fallback to ALL
            setting = WithdrawalSettingsDAO.Instance
                .GetWithdrawalSettingByTypeAndApplyFor("WITHDRAWAL_LIMIT", "ALL");
        }
        
        // Validate amount
        if (setting != null)
        {
            if (amount < setting.MinAmount)
            {
                return Json(new { success = false, message = $"Số tiền rút tối thiểu là {setting.MinAmountFormatted}" });
            }
            
            if (amount > setting.MaxAmount)
            {
                return Json(new { success = false, message = $"Số tiền rút tối đa là {setting.MaxAmountFormatted}" });
            }
        }
        
        // Tiếp tục logic rút tiền...
    }
}
```

### 7.2 Kiểm tra hạn mức hàng ngày
```csharp
// Lấy cài đặt hạn mức hàng ngày
var dailyLimit = WithdrawalSettingsDAO.Instance
    .GetWithdrawalSettingByTypeAndApplyFor("DAILY_LIMIT", userType);

if (dailyLimit != null)
{
    // Tính tổng đã rút trong ngày
    var todayWithdrawn = GetTodayWithdrawnAmount(userId);
    
    if (todayWithdrawn + amount > dailyLimit.MaxAmount)
    {
        return Json(new { success = false, message = "Vượt quá hạn mức rút tiền trong ngày" });
    }
}
```

## Bước 8: Monitoring và Logging

### 8.1 Kiểm tra Log
Logs được ghi trong thư mục `_LOG/` với format:
- `YYYY-MM-DD_Admin.log`

### 8.2 Monitor Database
```sql
-- Kiểm tra hoạt động
SELECT TOP 10 * FROM WithdrawalSettings ORDER BY CreatedDate DESC

-- Kiểm tra cài đặt active
SELECT SettingType, ApplyFor, COUNT(*) as Count
FROM WithdrawalSettings 
WHERE IsActive = 1
GROUP BY SettingType, ApplyFor
```

## Troubleshooting

### Lỗi "Không có quyền truy cập"
- Kiểm tra tài khoản admin có trong `_acceptListAdmin`
- Kiểm tra session admin đã đăng nhập

### Lỗi Database
- Kiểm tra connection string
- Kiểm tra stored procedures đã được tạo
- Kiểm tra quyền database user

### JavaScript không hoạt động
- Kiểm tra đường dẫn file `withdrawal-settings.js`
- Kiểm tra console browser có lỗi không
- Kiểm tra jQuery đã được load

### Grid không hiển thị dữ liệu
- Kiểm tra action `GetWithdrawalSettings` hoạt động
- Kiểm tra format JSON response
- Kiểm tra Telerik Grid configuration

## Backup và Restore

### Backup cài đặt
```sql
-- Export dữ liệu
SELECT * FROM WithdrawalSettings
-- Lưu kết quả ra file Excel/CSV
```

### Restore cài đặt
```sql
-- Import từ backup
INSERT INTO WithdrawalSettings (SettingName, Description, MinAmount, MaxAmount, SettingType, ApplyFor, CreatedBy)
VALUES 
-- Dữ liệu từ backup
```

## Cập nhật và Bảo trì

### Thêm loại cài đặt mới
1. Cập nhật enum trong `GetSettingTypes()`
2. Cập nhật `SettingTypeText` property
3. Test với dữ liệu mới

### Thêm loại user mới
1. Cập nhật enum trong `GetApplyForOptions()`
2. Cập nhật `ApplyForText` property
3. Cập nhật logic tích hợp

Chúc bạn cài đặt thành công! 🎉
