using System;
using System.ComponentModel.DataAnnotations;

namespace MsWebGame.CSKH.Models
{
    /// <summary>
    /// Model cho cài đặt MIN/MAX rút tiền
    /// </summary>
    public class WithdrawalSettingsModel
    {
        public int ID { get; set; }

        [Required(ErrorMessage = "Tên cài đặt không được để trống")]
        [Display(Name = "Tên cài đặt")]
        public string SettingName { get; set; }

        [Required(ErrorMessage = "<PERSON>ô tả không được để trống")]
        [Display(Name = "Mô tả")]
        public string Description { get; set; }

        [Required(ErrorMessage = "Giá trị tối thiểu không được để trống")]
        [Display(Name = "Giá trị tối thiểu (VND)")]
        [Range(0, long.MaxValue, ErrorMessage = "Gi<PERSON> trị tối thiểu phải lớn hơn hoặc bằng 0")]
        public long MinAmount { get; set; }

        [Required(ErrorMessage = "Giá trị tối đa không được để trống")]
        [Display(Name = "Giá trị tối đa (VND)")]
        [Range(1, long.MaxValue, ErrorMessage = "Giá trị tối đa phải lớn hơn 0")]
        public long MaxAmount { get; set; }

        [Display(Name = "Trạng thái")]
        public bool IsActive { get; set; }

        [Display(Name = "Ngày tạo")]
        public DateTime CreatedDate { get; set; }

        [Display(Name = "Ngày cập nhật")]
        public DateTime? UpdatedDate { get; set; }

        [Display(Name = "Người tạo")]
        public string CreatedBy { get; set; }

        [Display(Name = "Người cập nhật")]
        public string UpdatedBy { get; set; }

        [Display(Name = "Ghi chú")]
        public string Note { get; set; }

        /// <summary>
        /// Loại cài đặt: WITHDRAWAL_LIMIT, DAILY_LIMIT, MONTHLY_LIMIT
        /// </summary>
        [Required(ErrorMessage = "Loại cài đặt không được để trống")]
        [Display(Name = "Loại cài đặt")]
        public string SettingType { get; set; }

        /// <summary>
        /// Áp dụng cho: ALL, VIP, NORMAL
        /// </summary>
        [Display(Name = "Áp dụng cho")]
        public string ApplyFor { get; set; }

        public WithdrawalSettingsModel()
        {
            IsActive = true;
            CreatedDate = DateTime.Now;
            ApplyFor = "ALL";
        }
    }

    /// <summary>
    /// Model cho tìm kiếm cài đặt rút tiền
    /// </summary>
    public class WithdrawalSettingsSearchModel
    {
        [Display(Name = "Loại cài đặt")]
        public string SettingType { get; set; }

        [Display(Name = "Tên cài đặt")]
        public string SettingName { get; set; }

        [Display(Name = "Trạng thái")]
        public bool? IsActive { get; set; }

        [Display(Name = "Áp dụng cho")]
        public string ApplyFor { get; set; }
    }

    /// <summary>
    /// DTO cho cài đặt rút tiền
    /// </summary>
    public class WithdrawalSettingsDTO
    {
        public int ID { get; set; }
        public string SettingName { get; set; }
        public string Description { get; set; }
        public long MinAmount { get; set; }
        public long MaxAmount { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public string CreatedBy { get; set; }
        public string UpdatedBy { get; set; }
        public string Note { get; set; }
        public string SettingType { get; set; }
        public string ApplyFor { get; set; }

        /// <summary>
        /// Định dạng số tiền hiển thị
        /// </summary>
        public string MinAmountFormatted => MinAmount.ToString("N0") + " VND";
        public string MaxAmountFormatted => MaxAmount.ToString("N0") + " VND";
        
        /// <summary>
        /// Trạng thái hiển thị
        /// </summary>
        public string StatusText => IsActive ? "Hoạt động" : "Tạm dừng";
        
        /// <summary>
        /// Loại cài đặt hiển thị
        /// </summary>
        public string SettingTypeText
        {
            get
            {
                switch (SettingType)
                {
                    case "WITHDRAWAL_LIMIT":
                        return "Hạn mức rút tiền";
                    case "DAILY_LIMIT":
                        return "Hạn mức rút tiền hàng ngày";
                    case "MONTHLY_LIMIT":
                        return "Hạn mức rút tiền hàng tháng";
                    default:
                        return SettingType;
                }
            }
        }

        /// <summary>
        /// Áp dụng cho hiển thị
        /// </summary>
        public string ApplyForText
        {
            get
            {
                switch (ApplyFor)
                {
                    case "ALL":
                        return "Tất cả";
                    case "VIP":
                        return "Thành viên VIP";
                    case "NORMAL":
                        return "Thành viên thường";
                    default:
                        return ApplyFor;
                }
            }
        }
    }
}
