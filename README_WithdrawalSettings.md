# Chức năng Quản lý Cài đặt MIN/MAX Rút tiền

## Tổng quan
Chức năng này cho phép admin cài đặt và quản lý các hạn mức rút tiền trong hệ thống, bao gồm:
- <PERSON><PERSON><PERSON> mức rút tiền cơ bản
- <PERSON>ạn mức rút tiền hàng ngày
- Hạn mức rút tiền hàng tháng
- Phân biệt theo loại thành viên (Tất cả, VIP, Thường)

## Cấu trúc Files

### 1. Models
- `Models/WithdrawalSettingsModel.cs` - Model chính cho cài đặt rút tiền
  - WithdrawalSettingsModel: Model cho form input
  - WithdrawalSettingsSearchModel: Model cho tìm kiếm
  - WithdrawalSettingsDTO: Data Transfer Object

### 2. Database
- `Database/DAO/WithdrawalSettingsDAO.cs` - Data Access Object
- `Database/SQL/WithdrawalSettings.sql` - <PERSON>ript tạo bảng và stored procedures

### 3. Controllers
- `Controllers/WithdrawalSettingsController.cs` - Controller xử lý logic nghiệp vụ

### 4. Views
- `Views/WithdrawalSettings/Index.cshtml` - Trang danh sách và tìm kiếm
- `Views/WithdrawalSettings/Create.cshtml` - Trang thêm mới
- `Views/WithdrawalSettings/Edit.cshtml` - Trang chỉnh sửa

### 5. JavaScript
- `Scripts/withdrawal-settings.js` - JavaScript xử lý frontend

## Cài đặt

### Bước 1: Chạy Script SQL
```sql
-- Chạy file Database/SQL/WithdrawalSettings.sql để tạo:
-- - Bảng WithdrawalSettings
-- - Các stored procedures
-- - Dữ liệu mẫu
```

### Bước 2: Thêm Route (nếu cần)
Thêm route trong `RouteConfig.cs` hoặc `Global.asax.cs`:
```csharp
routes.MapRoute(
    name: "WithdrawalSettings",
    url: "WithdrawalSettings/{action}/{id}",
    defaults: new { controller = "WithdrawalSettings", action = "Index", id = UrlParameter.Optional }
);
```

### Bước 3: Thêm Menu
Thêm menu trong layout hoặc navigation:
```html
<li><a href="/WithdrawalSettings">Cài đặt rút tiền</a></li>
```

## Sử dụng

### 1. Truy cập chức năng
- URL: `/WithdrawalSettings`
- Yêu cầu quyền: Admin/CallCenter

### 2. Các chức năng chính

#### Xem danh sách
- Hiển thị tất cả cài đặt rút tiền
- Tìm kiếm theo: Loại cài đặt, Tên, Trạng thái, Áp dụng cho
- Phân trang

#### Thêm mới cài đặt
- Điền thông tin: Tên, Mô tả, Loại cài đặt, Áp dụng cho
- Nhập MIN/MAX amount
- Validation tự động

#### Chỉnh sửa cài đặt
- Cập nhật thông tin cài đặt
- Validation business rules

#### Xóa cài đặt
- Soft delete (chuyển IsActive = false)
- Xác nhận trước khi xóa

### 3. Loại cài đặt
- `WITHDRAWAL_LIMIT`: Hạn mức rút tiền cơ bản
- `DAILY_LIMIT`: Hạn mức rút tiền hàng ngày  
- `MONTHLY_LIMIT`: Hạn mức rút tiền hàng tháng

### 4. Áp dụng cho
- `ALL`: Tất cả thành viên
- `VIP`: Thành viên VIP
- `NORMAL`: Thành viên thường

## API Endpoints

### GET /WithdrawalSettings
Trang danh sách chính

### GET /WithdrawalSettings/GetWithdrawalSettings
Ajax endpoint lấy danh sách (cho grid)

### GET /WithdrawalSettings/Create
Trang thêm mới

### POST /WithdrawalSettings/Create
Xử lý thêm mới

### GET /WithdrawalSettings/Edit/{id}
Trang chỉnh sửa

### POST /WithdrawalSettings/Edit
Xử lý cập nhật

### POST /WithdrawalSettings/Delete
Ajax endpoint xóa cài đặt

## Database Schema

### Bảng WithdrawalSettings
```sql
CREATE TABLE [WithdrawalSettings](
    [ID] [int] IDENTITY(1,1) NOT NULL,
    [SettingName] [nvarchar](100) NOT NULL,
    [Description] [nvarchar](500) NOT NULL,
    [MinAmount] [bigint] NOT NULL DEFAULT(0),
    [MaxAmount] [bigint] NOT NULL DEFAULT(0),
    [IsActive] [bit] NOT NULL DEFAULT(1),
    [CreatedDate] [datetime] NOT NULL DEFAULT(GETDATE()),
    [UpdatedDate] [datetime] NULL,
    [CreatedBy] [nvarchar](50) NOT NULL,
    [UpdatedBy] [nvarchar](50) NULL,
    [Note] [nvarchar](1000) NULL,
    [SettingType] [nvarchar](50) NOT NULL,
    [ApplyFor] [nvarchar](20) NOT NULL DEFAULT('ALL')
)
```

### Stored Procedures
- `SP_WithdrawalSettings_GetList` - Lấy danh sách có phân trang
- `SP_WithdrawalSettings_GetById` - Lấy theo ID
- `SP_WithdrawalSettings_Create` - Thêm mới
- `SP_WithdrawalSettings_Update` - Cập nhật
- `SP_WithdrawalSettings_Delete` - Xóa (soft delete)
- `SP_WithdrawalSettings_GetByTypeAndApplyFor` - Lấy theo loại và áp dụng

## Validation Rules

### Frontend
- Tên cài đặt: Bắt buộc, tối thiểu 3 ký tự
- Mô tả: Bắt buộc
- MIN Amount: Bắt buộc, >= 0
- MAX Amount: Bắt buộc, > MIN Amount
- Loại cài đặt: Bắt buộc

### Backend
- Tên cài đặt không được trùng
- Mỗi loại cài đặt + áp dụng cho chỉ có 1 cài đặt active
- MIN < MAX amount

## Logging
- Ghi log khi tạo/sửa/xóa cài đặt
- Sử dụng NLogManager

## Security
- Chỉ admin trong danh sách được phép truy cập
- Kiểm tra quyền ở mỗi action
- Validate input để tránh injection

## Troubleshooting

### Lỗi thường gặp
1. **Không có quyền truy cập**: Kiểm tra tài khoản admin có trong danh sách `_acceptListAdmin`
2. **Lỗi database**: Kiểm tra connection string và stored procedures
3. **JavaScript không hoạt động**: Kiểm tra đường dẫn file JS

### Debug
- Kiểm tra log trong `_LOG` folder
- Sử dụng browser developer tools
- Kiểm tra database với SQL Management Studio

## Tích hợp với hệ thống rút tiền

Để sử dụng cài đặt này trong logic rút tiền:

```csharp
// Lấy cài đặt cho user
var userType = user.IsVIP ? "VIP" : "NORMAL";
var setting = WithdrawalSettingsDAO.Instance.GetWithdrawalSettingByTypeAndApplyFor("WITHDRAWAL_LIMIT", userType);

if (setting == null)
{
    // Fallback to ALL
    setting = WithdrawalSettingsDAO.Instance.GetWithdrawalSettingByTypeAndApplyFor("WITHDRAWAL_LIMIT", "ALL");
}

// Validate amount
if (withdrawAmount < setting.MinAmount || withdrawAmount > setting.MaxAmount)
{
    // Reject withdrawal
}
```
