/**
 * JavaScript cho chức năng quản lý cài đặt MIN/MAX rút tiền
 * Author: Admin System
 * Created: 2025
 */

var WithdrawalSettings = {
    // Khởi tạo
    init: function () {
        this.bindEvents();
        this.initializeNumberInputs();
        this.initializeValidation();
    },

    // Gắn các sự kiện
    bindEvents: function () {
        var self = this;

        // Sự kiện tìm kiếm
        $('#btnSearch').on('click', function () {
            self.searchSettings();
            return false;
        });

        // Sự kiện làm mới
        $('#btnReset').on('click', function () {
            self.resetSearch();
            return false;
        });

        // Sự kiện Enter trong ô tìm kiếm
        $('#settingName').on('keypress', function (e) {
            if (e.which === 13) {
                self.searchSettings();
                return false;
            }
        });

        // Sự kiện format số tiền
        $('.number-input').on('input', function () {
            self.formatNumberInput($(this));
        });

        // Sự kiện validate khi blur
        $('#SettingName').on('blur', function () {
            self.validateSettingName($(this));
        });

        $('#MinAmount, #MaxAmount').on('blur', function () {
            self.validateAmounts();
        });

        // Sự kiện submit form
        $('form').on('submit', function () {
            return self.beforeSubmit();
        });
    },

    // Khởi tạo các input số
    initializeNumberInputs: function () {
        var self = this;
        $('.number-input').each(function () {
            var value = $(this).val();
            if (value && !isNaN(value) && value !== '0') {
                $(this).val(self.formatNumber(parseInt(value)));
            }
        });
    },

    // Khởi tạo validation
    initializeValidation: function () {
        // Thêm các rule validation tùy chỉnh nếu cần
    },

    // Tìm kiếm cài đặt
    searchSettings: function () {
        try {
            var grid = $('#withdrawal-settings-grid').data('tGrid');
            if (grid) {
                grid.currentPage = 1;
                grid.ajaxRequest();
            }
        } catch (e) {
            console.error('Lỗi khi tìm kiếm:', e);
            this.showMessage('Có lỗi xảy ra khi tìm kiếm', 'error');
        }
    },

    // Làm mới tìm kiếm
    resetSearch: function () {
        try {
            $('#settingType').val('');
            $('#settingName').val('');
            $('#applyFor').val('');
            $('#isActive').val('');

            var grid = $('#withdrawal-settings-grid').data('tGrid');
            if (grid) {
                grid.currentPage = 1;
                grid.ajaxRequest();
            }
        } catch (e) {
            console.error('Lỗi khi làm mới:', e);
        }
    },

    // Xóa cài đặt
    deleteSetting: function (element) {
        var self = this;
        
        if (!confirm('Bạn có chắc chắn muốn xóa cài đặt này?')) {
            return;
        }

        try {
            var grid = $('#withdrawal-settings-grid').data('tGrid');
            var dataItem = grid.dataItem($(element).closest('tr'));

            $.ajax({
                url: '/WithdrawalSettings/Delete',
                type: 'POST',
                data: { id: dataItem.ID },
                success: function (result) {
                    if (result.success) {
                        self.showMessage(result.message, 'success');
                        grid.ajaxRequest();
                    } else {
                        self.showMessage(result.message, 'error');
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Lỗi AJAX:', error);
                    self.showMessage('Có lỗi xảy ra khi xóa cài đặt', 'error');
                }
            });
        } catch (e) {
            console.error('Lỗi khi xóa:', e);
            this.showMessage('Có lỗi xảy ra khi xóa cài đặt', 'error');
        }
    },

    // Format input số
    formatNumberInput: function (input) {
        var value = input.val().replace(/[^0-9]/g, '');
        if (value) {
            input.val(this.formatNumber(parseInt(value)));
        }
    },

    // Format số với dấu phẩy
    formatNumber: function (number) {
        return number.toLocaleString('vi-VN');
    },

    // Parse số từ string có format
    parseNumber: function (formattedNumber) {
        return parseInt(formattedNumber.replace(/[^0-9]/g, '') || '0');
    },

    // Validate tên cài đặt
    validateSettingName: function (input) {
        var value = input.val().trim();
        if (value.length < 3) {
            this.showFieldError(input, 'Tên cài đặt phải có ít nhất 3 ký tự');
            return false;
        } else {
            this.hideFieldError(input);
            return true;
        }
    },

    // Validate số tiền
    validateAmounts: function () {
        var minAmount = this.parseNumber($('#MinAmount').val());
        var maxAmount = this.parseNumber($('#MaxAmount').val());

        if (minAmount >= maxAmount && maxAmount > 0) {
            this.showFieldError($('#MaxAmount'), 'Giá trị tối đa phải lớn hơn giá trị tối thiểu');
            return false;
        } else {
            this.hideFieldError($('#MaxAmount'));
            return true;
        }
    },

    // Validate toàn bộ form
    validateForm: function () {
        var isValid = true;

        // Validate tên cài đặt
        if (!this.validateSettingName($('#SettingName'))) {
            isValid = false;
        }

        // Validate số tiền
        if (!this.validateAmounts()) {
            isValid = false;
        }

        // Validate required fields
        $('[data-val-required]').each(function () {
            var input = $(this);
            if (!input.val().trim()) {
                WithdrawalSettings.showFieldError(input, 'Trường này là bắt buộc');
                isValid = false;
            }
        });

        return isValid;
    },

    // Xử lý trước khi submit
    beforeSubmit: function () {
        // Remove formatting từ number inputs
        $('.number-input').each(function () {
            var value = $(this).val().replace(/[^0-9]/g, '');
            $(this).val(value);
        });

        // Validate form
        return this.validateForm();
    },

    // Hiển thị lỗi field
    showFieldError: function (field, message) {
        field.addClass('input-validation-error');
        var errorSpan = field.next('.field-validation-error');
        if (errorSpan.length === 0) {
            field.after('<span class="field-validation-error">' + message + '</span>');
        } else {
            errorSpan.text(message);
        }
    },

    // Ẩn lỗi field
    hideFieldError: function (field) {
        field.removeClass('input-validation-error');
        field.next('.field-validation-error').remove();
    },

    // Hiển thị thông báo
    showMessage: function (message, type) {
        type = type || 'info';
        
        // Remove existing messages
        $('.message-box').remove();
        
        // Create new message
        var messageClass = type === 'success' ? 'success' : 'error';
        var messageHtml = '<div class="message-box ' + messageClass + '">' + message + '</div>';
        
        // Insert message
        if ($('.section-header').length > 0) {
            $('.section-header').after(messageHtml);
        } else {
            $('body').prepend(messageHtml);
        }
        
        // Auto hide after 5 seconds
        setTimeout(function () {
            $('.message-box').fadeOut();
        }, 5000);
    },

    // Reset form
    resetForm: function () {
        $('form')[0].reset();
        $('.input-validation-error').removeClass('input-validation-error');
        $('.field-validation-error').remove();
        this.initializeNumberInputs();
    },

    // Utility: Debounce function
    debounce: function (func, wait, immediate) {
        var timeout;
        return function () {
            var context = this, args = arguments;
            var later = function () {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            var callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }
};

// Global functions for grid events
function deleteWithdrawalSetting(element) {
    WithdrawalSettings.deleteSetting(element);
}

function resetForm() {
    WithdrawalSettings.resetForm();
}

// Initialize when document ready
$(document).ready(function () {
    WithdrawalSettings.init();
});
