﻿@{
    ViewBag.Title = "Mở - Khóa đại lý";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@using MsWebGame.CSKH.Utils
@model MsWebGame.CSKH.Database.DTO.AgencyManager

@using (Html.BeginForm())
{
    <div class="section-header">
        <div class="title">
            Mở - Khóa đại lý
        </div>
    </div>
    <div class="t-widget t-tabstrip t-header">
        <div class="t-content t-state-active" style="display: block;">
            <table class="adminContent customer-info-tab">
                <tr>
                    <td class="adminTitle"></td>
                    <td class="adminData">
                        <span id="txttransferMsg" class="@(ViewBag.Message == "Mở khóa đại lý thành công" ? "txttransferMsgSuccess" : "txttransferMsg")">@ViewBag.Message</span>
                    </td>
                </tr>
                @if (Model != null && Model.NickName != null)
                {
                    <tr>
                        <td class="adminTitle">
                            <img src="~/Content/images/ico-help.gif">
                            <label>Nickname:</label>
                        </td>
                        <td class="adminData">
                            <input id="nickname" name="nickName" class="text-box single-line" value="@Model.NickName"
                                   onblur="GlobalHeader.validateNickName(2);" onkeypress="GlobalHeader.inputKeypress(event.keyCode);" />
                        </td>
                    </tr>
                    <tr>
                        <td class="adminTitle">
                            <img src="~/Content/images/ico-help.gif">
                            <label>Chọn trạng thái:</label>
                        </td>
                        <td class="adminData">
                            <select id="status" name="status" class="text-box single-line">
                                <option value="0" @(Model.Status == 0 ? "selected" : string.Empty)>Mở</option>
                                <option value="1" @(Model.Status == 1 ? "selected" : string.Empty)>Khóa</option>
                            </select>
                        </td>
                    </tr>
                }
                else
                {
                    <tr>
                        <td class="adminTitle">
                            <img src="~/Content/images/ico-help.gif">
                            <label>Nickname:</label>
                        </td>
                        <td class="adminData">
                            <input id="nickname" name="nickName" class="text-box single-line" onblur="GlobalHeader.validateNickName(2);"
                                   onkeypress="GlobalHeader.inputKeypress(event.keyCode);" />
                        </td>
                    </tr>
                    <tr>
                        <td class="adminTitle">
                            <img src="~/Content/images/ico-help.gif">
                            <label>Chọn trạng thái:</label>
                        </td>
                        <td class="adminData">
                            <select id="status" name="status" class="text-box single-line">
                                <option value="0">Mở</option>
                                <option value="1">Khóa</option>
                            </select>
                        </td>
                    </tr>
                }
                <tr>
                    <td class="adminTitle"></td>
                    <td class="adminData">
                        <input type="submit" class="t-button" value="@AppConstants.CONFIG.CONFIRM">
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <script type="text/javascript">
        $(document).ready(function () {
            setTimeout(function () {
                $('#txttransferMsg').html('');
            }, 4000);
        });
    </script>
}