﻿@using MsWebGame.CSKH.Models.AgencyRefund

@model List<AgencyRefundModel>

@{
    var name = (string)ViewBag.Name;

    var totalRecord = (int)ViewBag.TotalRecord;
    var startDate = (DateTime)ViewBag.StartDate;
    var endDate = (DateTime)ViewBag.EndDate;

    var pageIndex = (int)ViewBag.PageIndex;
    var pageSize = (int)ViewBag.PageSize;
}


<div class="agency-history-refund-card container">
    <h1 class="h1 text-center">Thông tin hoàn cược</h1>
    <form>
        <div style="display:flex; position: relative; margin-bottom: 10px; justify-content: flex-end; align-items: center">
            <label style="font-size: 16px; margin-right: 5px; margin-bottom: 0px;">Nhập tên đại lý:</label>
            <input style="width: fit-content; margin-right:10px" class="form-control" type="text" id="name" name="name" value="@name" />

            <label style="font-size: 16px; margin-right: 5px; margin-bottom: 0px;">Từ:</label>
            <input style="width: fit-content; margin-right:10px" class="form-control" type="date" id="StartDate" name="startDate" value="@(string.Format("{0}-{1}-{2}", startDate.Year,startDate.Month > 9 ? startDate.Month.ToString() : "0" + startDate.Month, startDate.Day > 9 ? startDate.Day.ToString() : "0" + startDate.Day))" />
            <label style="font-size: 16px; margin-right: 5px; margin-bottom: 0px; ">Đến:</label>
            <input style="width: fit-content; margin-right: 10px" class="form-control" type="date" id="EndDate" name="endDate" value="@(string.Format("{0}-{1}-{2}", endDate.Year,endDate.Month > 9 ? endDate.Month.ToString() : "0" + endDate.Month, endDate.Day > 9 ? endDate.Day.ToString() : "0" + endDate.Day))" />
            <input hidden id="PageIndex" name="pageIndex" value="@pageIndex" />
            <input hidden id="PageSize" name="pageSize" value="@pageSize" />

            <button type="submit" class="btn btn-primary">Lọc</button>
        </div>
    </form>

    <table class="table table-bordered table-striped table-hover">
        <thead>
            <tr>
                <td>AccountId</td>
                <td>AccountName</td>
                <td>DisplayName</td>
                <td style="text-align:center">AccountLevel</td>
                <td>TelegramID</td>
                <td>FullName</td>
                <td>PhoneDisplay</td>
                <td>FBLink</td>
                <td style="text-align:right">TotalRefund</td>
                <td></td>
            </tr>
        </thead>
        <tbody>
            @foreach (var agencyRefund in Model)
            {
                <tr>
                    <td>@agencyRefund.AccountId</td>
                    <td>@agencyRefund.AccountName</td>
                    <td>@agencyRefund.DisplayName</td>
                    <td style="text-align:center">@agencyRefund.AccountLevel</td>
                    <td>@agencyRefund.TelegramID</td>
                    <td>@agencyRefund.FullName</td>
                    <td>@agencyRefund.PhoneDisplay</td>
                    <td>@agencyRefund.FBLink</td>
                    <td style="text-align:right">@agencyRefund.TotalRefund.ToString("#,#")</td>
                    <td style="text-align:center"><a href="./AgencyRefundHistory?AgencyId=@agencyRefund.AccountId&StartDate=@startDate&EndDate=@endDate">Chi tiết</a></td>
                </tr>
            }
        </tbody>
    </table>
    <div class="reward-page-bottom">
        <div>
            <div style="font-weight:bold">Trang: @pageIndex / @(totalRecord % pageSize ==  0 ? totalRecord / pageSize : totalRecord / pageSize + 1)</div>
            <div style="font-weight:bold">
                Kích thước
                <select class="select-page-size">
                    <option value="name=@name&startDate=@startDate&endDate=@endDate&pageIndex=@(pageIndex)&pageSize=5" @(@pageSize == 5 ? "selected" : "")>5</option>
                    <option value="name=@name&startDate=@startDate&endDate=@endDate&pageIndex=@(pageIndex)&pageSize=10" @(@pageSize == 10 ? "selected" : "")>10</option>
                    <option value="name=@name&startDate=@startDate&endDate=@endDate&pageIndex=@(pageIndex)&pageSize=25" @(@pageSize == 25 ? "selected" : "")>25</option>
                    <option value="name=@name&startDate=@startDate&endDate=@endDate&pageIndex=@(pageIndex)&pageSize=50" @(@pageSize == 50 ? "selected" : "")>50</option>
                    <option value="name=@name&startDate=@startDate&endDate=@endDate&pageIndex=@(pageIndex)&pageSize=100" @(@pageSize == 100 ? "selected" : "")>100</option>
                </select> bản ghi / @(totalRecord) bản ghi
            </div>
        </div>
        <div class="flex relative" style="width: 150px;justify-content:space-between">
            @if (pageIndex > 1)
            {
                <a href="?name=@name&startDate=@startDate&endDate=@endDate&pageIndex=@(pageIndex - 1)&pageSize=@pageSize">Trang trước</a>
            }

            @if (pageSize * pageIndex < totalRecord)
            {
                <a href="?aname=@name&startDate=@startDate&endDate=@endDate&pageIndex=@(pageIndex + 1)&pageSize=@pageSize">Trang sau</a>
            }
        </div>
    </div>
</div>
@section css {
    <style>

        .agency-history-refund-card {
            box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
            border-radius: 5px
        }

        .reward-page-bottom {
            display: flex;
            position: relative;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .flex {
            display: flex;
        }

        .relative {
            position: relative;
        }
    </style>
}

@section scripts {
    <script src="~/Scripts/agency-refund-history/index.js"></script>
}
