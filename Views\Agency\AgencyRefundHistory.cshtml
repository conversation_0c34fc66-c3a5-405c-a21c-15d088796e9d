﻿@using MsWebGame.CSKH.Models
@using MsWebGame.CSKH.Models.AgencyRefundHistory

@model List<AgencyRefundHistoryModel>
@{
    var agencyId = (int)ViewBag.AgencyId;

    var totalRecord = (int)ViewBag.TotalRecord;
    var startDate = (DateTime)ViewBag.StartDate;
    var endDate = (DateTime)ViewBag.EndDate;

    var pageIndex = (int)ViewBag.PageIndex;
    var pageSize = (int)ViewBag.PageSize;
}

<div class="agency-history-refund-card container">
    <h1 class="h1 text-center"><PERSON><PERSON><PERSON> sử hoàn cược</h1>
    <form>
        <div style="display:flex; position: relative; margin-bottom: 10px; justify-content: flex-end; align-items: center">
            <label style="font-size: 16px; margin-right: 5px; margin-bottom: 0px;">Từ:</label>
            <input style="width: fit-content; margin-right:10px" class="form-control" type="date" id="StartDate" name="startDate" value="@(string.Format("{0}-{1}-{2}", startDate.Year,startDate.Month > 9 ? startDate.Month.ToString() : "0" + startDate.Month, startDate.Day > 9 ? startDate.Day.ToString() : "0" + startDate.Day))" />
            <label style="font-size: 16px; margin-right: 5px; margin-bottom: 0px; ">Đến:</label>
            <input style="width: fit-content; margin-right: 10px" class="form-control" type="date" id="EndDate" name="endDate" value="@(string.Format("{0}-{1}-{2}", endDate.Year,endDate.Month > 9 ? endDate.Month.ToString() : "0" + endDate.Month, endDate.Day > 9 ? endDate.Day.ToString() : "0" + endDate.Day))" />
            <input hidden id="PageIndex" name="pageIndex" value="@pageIndex" />
            <input hidden id="PageSize" name="pageSize" value="@pageSize" />
            <input hidden id="AgencyId" name="agencyId" value="@agencyId" />

            <button type="submit" class="btn btn-primary">Lọc</button>
        </div>
    </form>

    <table class="table table-bordered table-striped table-hover">
        <thead>
            <tr>
                <td>Id</td>
                <td>UserName</td>
                <td>DisplayName</td>
                <td>HistoryPlayId</td>
                <td>GameName</td>
                <td>BetAmount</td>
                <td>PercentRefund</td>
                <td>AmountRefund</td>
                <td>SpindID</td>
                <td>Description</td>
                <td>PayTime</td>
                <td>CDate</td>
            </tr>
        </thead>
        <tbody>
            @foreach (var history in Model)
            {
                <tr>
                    <td>@history.Id</td>
                    <td>@history.UserName</td>
                    <td>@history.DisplayName</td>
                    <td>@history.HistoryPlayId</td>
                    <td>@history.GameName</td>
                    <td>@history.BetAmount</td>
                    <td>@history.PercentRefund</td>
                    <td>@history.AmountRefund</td>
                    <td>@history.SpinID</td>
                    <td>@history.Description</td>
                    <td>@history.PayTime</td>
                    <td>@history.CDate</td>
                </tr>
            }
        </tbody>
    </table>
    <div class="reward-page-bottom">
        <div>
            <div style="font-weight:bold">Trang: @pageIndex / @(totalRecord % pageSize ==  0 ? totalRecord / pageSize : totalRecord / pageSize + 1)</div>
            <div style="font-weight:bold">
                Kích thước
                <select class="select-page-size">
                    <option value="agencyId=@agencyId&startDate=@startDate&endDate=@endDate&pageIndex=@(pageIndex)&pageSize=5" @(@pageSize == 5 ? "selected" : "")>5</option>
                    <option value="agencyId=@agencyId&startDate=@startDate&endDate=@endDate&pageIndex=@(pageIndex)&pageSize=10" @(@pageSize == 10 ? "selected" : "")>10</option>
                    <option value="agencyId=@agencyId&startDate=@startDate&endDate=@endDate&pageIndex=@(pageIndex)&pageSize=25" @(@pageSize == 25 ? "selected" : "")>25</option>
                    <option value="agencyId=@agencyId&startDate=@startDate&endDate=@endDate&pageIndex=@(pageIndex)&pageSize=50" @(@pageSize == 50 ? "selected" : "")>50</option>
                    <option value="agencyId=@agencyId&startDate=@startDate&endDate=@endDate&pageIndex=@(pageIndex)&pageSize=100" @(@pageSize == 100 ? "selected" : "")>100</option>
                </select> bản ghi / @(totalRecord) bản ghi
            </div>
        </div>
        <div class="flex relative" style="width: 150px;justify-content:space-between">
            @if (pageIndex > 1)
            {
                <a href="?agencyId=@agencyId&startDate=@startDate&endDate=@endDate&pageIndex=@(pageIndex - 1)&pageSize=@pageSize">Trang trước</a>
            }

            @if (pageSize * pageIndex < totalRecord)
            {
                <a href="?agencyId=@agencyId&startDate=@startDate&endDate=@endDate&pageIndex=@(pageIndex + 1)&pageSize=@pageSize">Trang sau</a>
            }
        </div>
    </div>
</div>
@section css {
    <style>

        .agency-history-refund-card {
            box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
            border-radius: 5px
        }

        .reward-page-bottom {
            display: flex;
            position: relative;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .flex {
            display: flex;
        }

        .relative {
            position: relative;
        }
    </style>
}

@section scripts {
    <script src="~/Scripts/agency-refund-history/index.js"></script>
}