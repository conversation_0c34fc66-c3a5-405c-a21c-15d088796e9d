﻿@{
    ViewBag.Title = "Luồng tiền đại lý";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
@using MsWebGame.CSKH.Database.DTO
@using Telerik.Web.Mvc.UI
@using MsWebGame.CSKH.Utils

@using (Html.BeginForm())
{
    <div class="section-header">
        <div class="title">
            <img src="@Url.Content("~/Content/images/ico-promotions.png")" alt="" />
            Luồng tiền đại lý
        </div>
    </div>
    <table style="width: 100%;">
        <tbody>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    <label>NickName</label>
                </td>
                <td class="adminData">
                    <input class="text-box single-line" id="nick-name" type="text">
                    <span class="field-validation-valid" data-valmsg-for="nick-name"></span>
                </td>
            </tr>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    <label>Từ ngày</label>
                </td>
                <td class="adminData">
                    @Html.Telerik().DatePicker().Name("from-date").Value(DateTime.Today.AddDays(-7))
                </td>
            </tr>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    <label>Đến ngày</label>
                </td>
                <td class="adminData">
                    @Html.Telerik().DatePicker().Name("to-date").Value(DateTime.Today)
                </td>
            </tr>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    <label>Chọn cổng</label>
                </td>
                <td class="adminData">
                    @Html.DropDownList("serviceId", new SelectList(ViewBag.ServiceBox, "Value", "Text"), new { @class = "text-box single-line" })
                </td>
            </tr>
            <tr>
                <td class="adminTitle">
                    &nbsp;
                </td>
                <td>
                    <input type="button" id="btnSearch" class="t-button" value="@AppConstants.CONFIG.SEARCH" style="margin-bottom: 6px;">
                </td>
            </tr>
        </tbody>
    </table>
    <table class="adminContent">
        <tr>
            <td>
                @(Html.Telerik().Grid<CashFlow>()
                    .Name("agc-cashflow-grid")
                    .ClientEvents(events => events.OnDataBinding("onDataBinding"))
                    .Columns(columns =>
                    {
                        columns.Bound(e => e.BuyAgencyTotalFormat).Title("Tổng mua đại lý").HtmlAttributes(new { @class = "text-right" });
                        columns.Bound(e => e.SellAgencyTotalFormat).Title("Tổng bán đại lý").HtmlAttributes(new { @class = "text-right" });
                        columns.Bound(e => e.DeltaAgencyFormat).Title("Chênh lệch mua bán đại lý").HtmlAttributes(new { @class = "text-right" });
                        columns.Bound(x => x.BuyUserTotalFormat).Title("Tổng mua người chơi").HtmlAttributes(new { @class = "text-right" });
                        columns.Bound(e => e.SellUserTotalFormat).Title("Tổng bán người chơi").HtmlAttributes(new { @class = "text-right" });
                        columns.Bound(e => e.DeltaUserFormat).Title("Chênh lệch mua bán người chơi").HtmlAttributes(new { @class = "text-right" });
                    })
                    .Pageable(settings => settings.PageSize(AppConstants.CONFIG.GRID_SIZE).Position(GridPagerPosition.Both))
                    .DataBinding(dataBinding => dataBinding.Ajax().Select("GetCashFlow", "Agency"))
                    .EnableCustomBinding(true))
            </td>
        </tr>
    </table>

    <script type="text/javascript">
        $(document).ready(function () {
            $('#btnSearch').click(function () {
                var grid = $('#agc-cashflow-grid').data('tGrid');
                grid.currentPage = 1; //new search. Set page size to 1
                grid.ajaxRequest();
                return false;
            });
        });

        function onDataBinding(e) {
            var searchModel = {
                nickName: $('#nick-name').val().trim(),
                fromDate: $('#from-date').val().trim(),
                toDate: $('#to-date').val().trim(),
                serviceId: $('#serviceId').val().trim()
            };
            e.data = searchModel;
        }
    </script>
}