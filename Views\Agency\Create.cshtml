﻿@using MsWebGame.CSKH.Utils
@model  MsWebGame.CSKH.Models.Agencies.AgencyModel
@{
    //page title
    ViewBag.Title = AppConstants.CONFIG.ADD_NEW;
}
@using (Html.BeginForm())
{
    <div class="section-header">
        <div class="title">
            <img src="@Url.Content("~/Content/images/ico-promotions.png")" alt="" />
            @AppConstants.CONFIG.ADD_NEW
        </div>
        <div class="options">
            <a href="@Url.Action("Index")" class="t-button">Quay lại</a>
        </div>
    </div>
    @Html.Partial("_CreateOrUpdate", Model)
}