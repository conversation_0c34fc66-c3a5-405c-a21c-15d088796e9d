﻿@using MsWebGame.CSKH.Utils
@model  MsWebGame.CSKH.Models.Agencies.AgencyModel
@{
    //page title
    ViewBag.Title = AppConstants.CONFIG.EDIT;
}
@using (Html.BeginForm())
{
    <div class="section-header">
        <div class="title">
            <img src="@Url.Content("~/Content/images/ico-promotions.png")" alt="" />
            @AppConstants.CONFIG.EDIT @Html.ActionLink("(" + AppConstants.CONFIG.BACK + ")", "Index")
        </div>
        <div class="options">
            <a href="@Url.Action("Index")" class="t-button">Quay lại</a>
        </div>
    </div>
    @Html.HiddenFor(m => m.AccountId)
    @Html.HiddenFor(m => m.AccountName)
    @Html.HiddenFor(m => m.DisplayName)
    @Html.HiddenFor(m => m.ServiceID)
  
    @Html.Partial("_CreateOrUpdate", Model)

   
}