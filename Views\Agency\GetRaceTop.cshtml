﻿@using TraditionGame.Utilities.Utils
@model List<MsWebGame.CSKH.Database.DTO.AgencyRaceTop>

<div class="t-widget t-grid" id="agc-racetop-grid">
    <table cellspacing="0">
        <thead class="t-grid-header">
            <tr>
                <th class="t-header" scope="col"><span class="t-link">Hạng</span></th>
                <th class="t-header" scope="col"><span class="t-link">NickName</span></th>
                <th class="t-header" scope="col"><span class="t-link">Tổng luồng giao dịch</span></th>
                <th class="t-header" scope="col"><span class="t-link">Tổng VIP</span></th>
                <th class="t-header" scope="col"><span class="t-link">Hệ số</span></th>
                <th class="t-header" scope="col"><span class="t-link">Thưởng</span></th>
            </tr>
        </thead>
        <tbody>
            @if (Model != null && Model.Any())
            {
                foreach (var item in Model)
                {
                    <tr>
                        <td class="text-right">@item.PrizeID</td>
                        <td>@item.DisplayName</td>
                        <td class="text-right">@MoneyExtension.LongToMoneyFormat(item.TotalTransfer)</td>
                        <td class="text-right">@MoneyExtension.DecimalToMoneyFormat(item.TotalVP)</td>
                        <td class="text-right">@MoneyExtension.IntToMoneyFormat(item.BonusRate)</td>
                        @if (ViewBag.IsClosed)
                        {
                            <td class="text-right">@MoneyExtension.LongToMoneyFormat(item.PrizeValue)</td>
                        }
                        else
                        {
                            <td class="text-right">@MoneyExtension.DecimalToMoneyFormat(item.TotalBom)</td>
                        }
                    </tr>
                }
            }
        </tbody>
        @if (Model != null && Model.Any())
        {
            <tfoot>
                <tr>
                    <td colspan="2"><span class="t-link"><b>Thống kê</b></span></td>
                    <td class="text-right"><span class="t-link"><b>@Model.Select(a => a.TotalTransfer).Sum().LongToMoneyFormat()</b> </span></td>
                    <td class="text-right"><span class="t-link"><b>@MoneyExtension.DecimalToMoneyFormat(Model.Select(a => a.TotalVP).Sum())</b></span></td>
                    <td class="text-right"><span class="t-link"><b>@Model.Select(a => a.BonusRate).Sum().IntToMoneyFormat() </b></span></td>
                    @if (ViewBag.IsClosed)
                    {
                        <td class="text-right"><span class="t-link"><b>@Model.Select(a => a.PrizeValue).Sum().LongToMoneyFormat() </b></span></td>
                    }
                    else
                    {
                        <td class="text-right"><span class="t-link"><b>@MoneyExtension.DecimalToMoneyFormat(Model.Select(a => a.TotalBom).Sum())</b></span></td>
                    }
                </tr>
            </tfoot>
        }
    </table>
</div>
