﻿
@{
    ViewBag.Title = "Reset Mật Khẩu Agency";
}

@using TraditionGame.Utilities.Utils;

@using (Html.BeginForm())
{
    <div class="section-header">
        <div class="title">Reset Mật Khẩu</div>
    </div>
    <div class="t-widget t-tabstrip t-header">
        <div class="t-content t-state-active" style="display: block;">
            <table class="adminContent customer-info-tab">
                <tr>
                    <td class="adminTitle"></td>
                    <td class="adminData">
                        <span id="txttransferMsg" class="@(ViewBag.Message == "Cập nhật thành công" ? "txttransferMsgSuccess" : "txttransferMsg")">@ViewBag.Message</span>
                    </td>
                </tr>
                <tr>
                    <td class="adminTitle">
                        <label>Nickname <span style="color:red;">*</span></label>
                    </td>
                    <td class="adminData">
                        <input class="text-box single-line" id="Username" name="Username" type="text">
                        <span class="field-validation-valid" data-valmsg-for="amount"></span>
                    </td>
                </tr>
                <tr>
                    <td class="adminTitle"></td>
                    <td class="adminData">
                        <input type="submit" name="save" class="t-button" value="Reset" />
                    </td>
                </tr>
            </table>
        </div>
    </div>


}