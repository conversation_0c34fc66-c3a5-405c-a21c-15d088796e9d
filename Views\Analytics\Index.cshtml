﻿@using MsWebGame.CSKH.Utils
@using Telerik.Web.Mvc.UI
@model List<Dictionary<string,string>>
@{
    ViewBag.Title = "Thông kê doanh thu";
}
<!-- Main content -->
<section class="content">
         <div class="box box-solid bg-teal-gradient">
             <div class="box-header">
                 <i class="fa fa-th"></i>

                 <h3 class="box-title"><strong>Thông kê theo ngày @ViewBag.StartDate  -  @ViewBag.EndDate</strong></h3><BR>

                 <div class="box-tools pull-right">
                     <button type="button" class="btn bg-teal btn-sm" data-widget="collapse"><i
                                 class="fa fa-minus"></i>
                     </button>
                     <button type="button" class="btn bg-teal btn-sm" data-widget="remove"><i
                                 class="fa fa-times"></i>
                     </button>
                 </div>
             </div>
             <div class="box-body border-radius-none" style="background:#000000">
                 <div class="chart" id="line-chart" style="height: 250px;"></div>
             </div>
         </div>
</section>
 <script src="https://cdnjs.cloudflare.com/ajax/libs/morris.js/0.4.2/morris.min.js"></script>
 <script src="https://cdnjs.cloudflare.com/ajax/libs/raphael/2.3.0/raphael.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/morris.js/0.4.2/morris.min.css">
 
<script>
var line = new Morris.Line({
            element: 'line-chart',
            resize: true,
            data:   @Html.Raw(new System.Web.Script.Serialization.JavaScriptSerializer().Serialize(Model)),
            xkey: 'date',
            ykeys: ['totalCharginCard', 'totalCharginMomo','totalCharginBank','totalOutBank'],
            labels: ['Tổng nạp Card', 'Tổng nạp Momo','Tổng nạp Bank','Tổng đổi Bank'],
            lineColors: ['#00c0ef', '#00a65a','#337ab7','#dd4b39', '#f39c12','blue','#000000'],
            lineWidth: 1,
            hideHover: 'auto',
            gridTextColor: '#fff',
            gridStrokeWidth: 0.4,
            pointSize: 5,
            pointStrokeColors: ['#efefef'],
            gridLineColor: '#efefef',
            gridTextFamily: 'Open Sans',
            gridTextSize: 10

        });      
</script>