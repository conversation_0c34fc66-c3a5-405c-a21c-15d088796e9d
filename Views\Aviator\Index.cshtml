﻿@{
    ViewBag.Title = "Aviator Game";
}

<style>
    .thead-dark {
        background: #ffc800;
    }

    .table {
        border: 1px solid #ffc800;
    }
</style>

<div class="container">
    <table class="table table-bordered">
        <tr>
            <td class="text-center">
                <p>PHIÊN</p>
                <strong id="SessionID"></strong>
            </td>
            <td class="text-center">
                <strong id="Multiplier" style="color: red; font-size: 30px;"></strong>
                <strong style="font-size: 30px;">x</strong>
                <div>
                    <button id="stopButton" onclick="StopFlying()" style="display: none;">DỪNG BAY</button>
                </div>
            </td>
            <td class="text-center">
                <p>TRẠNG THÁI</p>
                <strong id="Phrase"></strong>
            </td>
        </tr>
    </table>
    <table class="table table-bordered" id="myform">
        <tr>
            <td id="kq_1" value="1" class="text-center" style="vertical-align: middle;">
                <p>TỰ CHỈNH</p>
            </td>
            <td id="kq_2" value="2" class="text-center" style="vertical-align: middle;">
                TỐI ƯU
            </td>
            <td id="kq_random" value="0" class="text-center" style="vertical-align: middle;">
                NGẪU NHIÊN
            </td>
        </tr>
        <tr>
            <td class="text-center">
                <input type="radio" name="mode" value="1" onclick="SetModel(1)">
            </td>
            <td class="text-center">
                <input type="radio" name="mode" value="2" onclick="SetModel(2)">
            </td>
            <td class="text-center">
                <input type="radio" name="mode" value="0" onclick="SetModel(0)" checked="true">
            </td>
        </tr>
    </table>
    <table class="table">
        <thead class="thead-dark">
            <tr>
                <th scope="col" style="text-align: center;">AccountID</th>
                <th scope="col" style="text-align: center;">Nickname</th>
                <th scope="col" style="text-align: center;">Cược</th>
                <th scope="col" style="text-align: center;">Trạng Thái</th>
                <th scope="col" style="text-align: center;">Thắng</th>
                <th scope="col" style="text-align: center;">Hệ Số Dừng</th>
            </tr>
        </thead>
        <tbody id="BetList">
        </tbody>
    </table>
</div>

<script>
    function GetSession() {
        $.ajax({
            type: "POST",
            url: "/Aviator/InitSession",
            dataType: "json",
            success: function (result) {
                $("#Elapsed").html(result.Elapsed);
                $("#SessionID").html(result.SessionID);
                $("#Phrase").html(result.Phrase);
                $("#Status").html(result.IsCrashed);
                $("#Multiplier").html(result.Multiplier);
                $(`#myform input[name=mode][value=${result.Model.Mode}]`).prop('checked', true);
                $("#BetList").html("");
                result.BetList.forEach(function (bet) {
                    var row = "<tr>" +
                        "<td class='text-center'>" + bet.AccountID + "</td>" +
                        "<td class='text-center'>" + bet.Nickname + "</td>" +
                        "<td class='text-center'>" + formatMoney(bet.BetValue) + "</td>" +
                        "<td class='text-center'>" + (bet.IsWon ? "Đã dừng" : "Đang Bay") + "</td>" +
                        "<td class='text-center'>" + formatMoney(bet.Award) + "</td>" +
                        "<td class='text-center'>" + bet.Multiplier + "</td>" +
                        "</tr>";
                    $("#BetList").append(row);
                }); 
                if (result.Phrase === "Flying") {
                    $("#stopButton").show();
                } else {
                    $("#stopButton").hide();
                }
            }
        });
    }

    function SetModel(val) {
        $.ajax({
            type: "POST",
            url: "/Aviator/SetModel",
            dataType: "json",
            data: { model: val ? val : $("#myform input[name='resulted']:checked").val() },
            success: function (result) {
                console.log(result);
                try {
                    if (result.IsOke) {

                    } else {
                        $("#myform input[name=mode][value=0]").prop('checked', true);
                    }
                } catch (e) {
                    location.reload();
                }
            }
        });
    }

    function StopFlying() {
        var data = {
            isStop: true
        };
        $.ajax({
            type: "POST",
            url: "/Aviator/StopFlying",
            dataType: "json",
            data: data,
            success: function (result) {
                console.log(result);
                try {
                    if (result.IsOke) {
                        console.log("ok");
                    }
                } catch (e) {
                    location.reload();
                }
            },
            error: function (xhr, status, error) {
                console.error("Error occurred: ", error);
            }
        });
    }

    function formatMoney(n, c, d, t) {
        var c = isNaN(c = Math.abs(c)) ? 0 : c,
            d = d == undefined ? "." : d,
            t = t == undefined ? "," : t,
            s = n < 0 ? "-" : "",
            i = String(parseInt(n = Math.abs(Number(n) || 0).toFixed(c))),
            j = (j = i.length) > 3 ? j % 3 : 0;

        return s + (j ? i.substr(0, j) + t : "") + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + t) + (c ? d + Math.abs(n - i).toFixed(c).slice(2) : "");
    }

    $(document).ready(function () {
        GetSession(true);
        setInterval(GetSession, 999);
    });
</script>
