﻿@using MsWebGame.CSKH.Utils
@using Telerik.Web.Mvc.UI
@using MsWebGame.CSKH.Models.Baccarat

@{
    ViewBag.Title = "Chỉnh cầu";
}
<style>
	/*#myform {
		width:80%
	}
	@@media only screen and (max-width: 1000px) {
	  #myform {
		width:90%
	  }
	}
	@@media only screen and (max-width: 600px) {
	  #myform {
		width:100%
	  }
	}*/
    .headerBaccarat {
        background: url('@Url.Content("~/Images/Baccarat/bg-tx.png")');
        background-repeat: no-repeat;
        height: 250px;
		background-size: 100% 100%;
		margin-bottom: 10px;
    }
    .headerBaccarat .item{
        text-align:center;
		margin-top: 8px;
		margin-bottom: 8px;
    }
	 .headerBaccarat .item strong{
        font-size: 25px;
		color: yellow;
		padding: 7px 10px;
		display: inline-block;
    }
	.thead-dark{
		background: #ffc800;
	}
	.table{
		border:1px solid #ffc800;
	}
	table tbody {
		display: block;
		max-height: 400px;
		min-height: 400px;
		overflow-y: scroll;
	}
	table thead, table tbody tr {
	  display: table;
	  width: 100%;
	  table-layout: fixed;
	}
	 table tbody td{
		text-align: center;
	 }
	 .clickResult{
		width: 50px;
		height: 50px;
		margin: 0 auto;
	 }
	 input[type="radio"] {
			-ms-transform: scale(1.5); /* IE 9 */
			-webkit-transform: scale(1.5); /* Chrome, Safari, Opera */
			transform: scale(1.5);
		}

	.light-win{
		position: absolute;
		animation-name: rotate;
		animation-duration: 5s;
		animation-iteration-count: infinite;
		animation-timing-function: linear;
	}

    .clickResult {
        min-height: 100px;
        background-color: #387826;
        width: 100%;
        display: flex;
        align-content: center;
        justify-content: center;
        padding: 10px 0;
        flex-direction: column-reverse;
        align-items: center;
        border: 2px solid #000;
    }

    .typeTitle {
        font-size: 30px;
        color: #fff;
    }

	@@keyframes rotate{
		from{ transform: rotate(-360deg); }
		to{ transform: rotate(360deg); }
	}
</style>
<div class="conrongner1" id="myform">
    <div class="row">

        <div class="col-md-4">
            <h1 id="SessionID"></h1>
        </div>
        <div class="col-md-4">
            <h1 id="CurrentState"></h1>
        </div>
        <div class="col-md-4">
            <h1 id="TimeCountdown"></h1>
        </div>

        <div class="col-md-4">
            <div class="clickResult" style="background-color: #657826; margin-right: 0; margin-bottom: 20px">
                <input type="radio" id="resultedPair1" name="resultedPair" value="1" onclick="SetModel()">
                <label for="resultedPair1" class="typeTitle">Player Pair</label>
            </div>
        </div>

        <div class="col-md-4">
            <div class="clickResult" style="background-color: #657826; margin-left: 0; margin-bottom: 20px">
                <input type="radio" id="resultedPair5" name="resultedPair" value="5" onclick="SetModel()">
                <label for="resultedPair5" class="typeTitle">Banker Pair</label>
            </div>
        </div>

        <div class="col-md-4">
            <div class="clickResult" style="background-color: #657826; margin-left: 0; margin-bottom: 20px">
                <input type="radio" id="resultedPair-1" name="resultedPair" value="-1" onclick="SetModel()">
                <label for="resultedPair-1" class="typeTitle">Random pair</label>
            </div>
        </div>

        <div class="col-md-3">
            <div class="clickResult">
                <input type="radio" id="resulted2" name="resulted" value="2" onclick="SetModel()">
                <label for="resulted2" class="typeTitle">Player</label>
            </div>
        </div>
        <div class="col-md-3">
            <div class="clickResult">
                <input type="radio" id="resulted3" name="resulted" value="3" onclick="SetModel()">
                <label for="resulted3" class="typeTitle">Tie</label>
            </div>
        </div>
        <div class="col-md-3">
            <div class="clickResult">
                <input type="radio" id="resulted4" name="resulted" value="4" onclick="SetModel()">
                <label for="resulted4" class="typeTitle">Banker</label>
            </div>
        </div>
        <div class="col-md-3">
            <div class="clickResult">
                <input type="radio" id="resulted-1" name="resulted" value="-1" onclick="SetModel()">
                <label for="resulted-1" class="typeTitle">Random</label>
            </div>
        </div>
        <div style="display: inline-flex;">
            <div style="width: calc(20% - 10px); margin-right: 5px; ">
                <BR>
                <div>
                    <h3><b>Player Pair </b></h3>
                    <h3>Tổng đặt : <strong id="totalBet_1">0</strong></h3>
                    <h3>Tổng cược : <strong id="total_1">0</strong></h3>
                </div>
                <BR>
                <table class="table">
                    <thead class="thead-dark">
                        <tr>
                            <th scope="col" style="text-align: center;">STT</th>
                            <th scope="col" style="text-align: center;">Mã</th>
                            <th scope="col" style="text-align: center;">Tên đăng nhập</th>
                            <th scope="col" style="text-align: center;">Cược</th>
                        </tr>
                    </thead>
                    <tbody id="BetSide_1">
                    </tbody>
                </table>

            </div>
            <div style="width: calc(20% - 10px); margin-right: 5px; ">
                <BR>
                <div>
                    <h3><b>Player </b></h3>
                    <h3>Tổng đặt : <strong id="totalBet_2">0</strong></h3>
                    <h3>Tổng cược : <strong id="total_2">0</strong></h3>
                </div>
                <BR>
                <table class="table">
                    <thead class="thead-dark">
                        <tr>
                            <th scope="col" style="text-align: center;">STT</th>
                            <th scope="col" style="text-align: center;">Mã</th>
                            <th scope="col" style="text-align: center;">Tên đăng nhập</th>
                            <th scope="col" style="text-align: center;">Cược</th>
                        </tr>
                    </thead>
                    <tbody id="BetSide_2">
                    </tbody>
                </table>

            </div>
            <div style="width: calc(20% - 10px); margin-right: 5px; ">
                <BR>
                <div>
                    <h3><b>Tie </b></h3>
                    <h3>Tổng đặt : <strong id="totalBet_3">0</strong></h3>
                    <h3>Tổng cược : <strong id="total_3">0</strong></h3>
                </div>
                <BR>
                <table class="table">
                    <thead class="thead-dark">
                        <tr>
                            <th scope="col" style="text-align: center;">STT</th>
                            <th scope="col" style="text-align: center;">Mã</th>
                            <th scope="col" style="text-align: center;">Tên đăng nhập</th>
                            <th scope="col" style="text-align: center;">Cược</th>
                        </tr>
                    </thead>
                    <tbody id="BetSide_3">
                    </tbody>
                </table>

            </div>
            <div style="width: calc(20% - 10px); margin-right: 5px; ">
                <BR>
                <div>
                    <h3><b>Banker </b></h3>
                    <h3>Tổng đặt : <strong id="totalBet_4">0</strong></h3>
                    <h3>Tổng cược : <strong id="total_4">0</strong></h3>
                </div>
                <BR>
                <table class="table">
                    <thead class="thead-dark">
                        <tr>
                            <th scope="col" style="text-align: center;">STT</th>
                            <th scope="col" style="text-align: center;">Mã</th>
                            <th scope="col" style="text-align: center;">Tên đăng nhập</th>
                            <th scope="col" style="text-align: center;">Cược</th>
                        </tr>
                    </thead>
                    <tbody id="BetSide_4">
                    </tbody>
                </table>
            </div>

            <div style="width: calc(20% - 10px); margin-right: 5px;">
                <BR>
                <div>
                    <h3><b>Banker Pair</b></h3>
                    <h3>Tổng đặt : <strong id="totalBet_5">0</strong></h3>
                    <h3>Tổng cược : <strong id="total_5">0</strong></h3>
                </div>
                <BR>
                <table class="table">
                    <thead class="thead-dark">
                        <tr>
                            <th scope="col" style="text-align: center;">STT</th>
                            <th scope="col" style="text-align: center;">Mã</th>
                            <th scope="col" style="text-align: center;">Tên đăng nhập</th>
                            <th scope="col" style="text-align: center;">Cược</th>
                        </tr>
                    </thead>
                    <tbody id="BetSide_5">
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script src="@Url.Content("~/Scripts/jquery.signalR-2.3.0.min.js")"></script>
<script src="/signalr/hubs"></script>
<script><!--const _WEB_URL = document.getElementById('rootDomain').href;-->
    let init = true;

    //Elements
    let txtSessionID = $('#SessionID');
    let txtTimeCountdown = $('#TimeCountdown');
    let txtCurrentState = $('#CurrentState');

    let txtTotal_5 = $("#total_5");
    let txtTotal_4 = $("#total_4");
    let txtTotal_3 = $("#total_3");
    let txtTotal_2 = $("#total_2");
    let txtTotal_1 = $("#total_1");

    let txtTotalBet_5 = $("#totalBet_5");
    let txtTotalBet_4 = $("#totalBet_4");
    let txtTotalBet_3 = $("#totalBet_3");
    let txtTotalBet_2 = $("#totalBet_2");
    let txtTotalBet_1 = $("#totalBet_1");

    let txtBetSide_1 = $("#BetSide_1");
    let txtBetSide_2 = $("#BetSide_2");
    let txtBetSide_3 = $("#BetSide_3");
    let txtBetSide_4 = $("#BetSide_4");
    let txtBetSide_5 = $("#BetSide_5");

    let rdPlayer = $("#myform input[name=resulted][value=2]");
    let rdTie = $("#myform input[name=resulted][value=3]");
    let rdBanker = $("#myform input[name=resulted][value=4]");
    let rdRandom = $("#myform input[name=resulted][value=-1]");
    let rdPlayerPair = $("#myform input[name=resultedPair][value=1]");
    let rdBankerPair = $("#myform input[name=resultedPair][value=5]");
    let rdRandomPair = $("#myform input[name=resultedPair][value=-1]");

    function formatMoney(n, c, d, t) {
        var c = isNaN(c = Math.abs(c)) ? 0 : c,
            d = d == undefined ? "." : d,
            t = t == undefined ? "," : t,
            s = n < 0 ? "-" : "",
            i = String(parseInt(n = Math.abs(Number(n) || 0).toFixed(c))),
            j = (j = i.length) > 3 ? j % 3 : 0;

        return s + (j ? i.substr(0, j) + t : "") + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + t) + (c ? d + Math.abs(n - i).toFixed(c).slice(2) : "");
    };

    function getRndInteger(min, max) {
        return Math.floor(Math.random() * (max - min)) + min;
    }

    function SetModel(val) {
        var model = $("#myform input[name='resulted']:checked").val();
        var modelPair = $("#myform input[name='resultedPair']:checked").val();
        var status = "";

        if ((!model && !modelPair) || (model == -1 && modelPair == -1))
            status = "-1"
        else {
            if (!model || model == -1) {
                model = "-1" // getRndInteger(2, 5)// random
            }

            if (modelPair)
                status = model + (modelPair != -1 ? ("," + modelPair) : "")
            else
                status = model
        }


        $.ajax({
            type: "POST",
            url: "/Baccarat/SetModel",
            dataType: "json",
            data: { model: status  },
            success: function (result) {
                try {
                    if (result.IsOke) {

                    } else {
                        $("#myform input[name=resulted][value=2]").prop('checked', true);
                    }
                } catch (e) {
                    location.reload();
                }
            }
        });
    }

    function GetSession() {
        $.ajax({
            type: "POST",
            url: "/Baccarat/InitSession",
            dataType: "json",
            success: function (result) {
                bindSession(result)
            }
        });
    }

    function bindSession(result) {
        if (init) {
            init = false;
        }

        if (result.Model == "-1") {
            rdRandomPair.prop('checked', true);
            rdRandom.prop('checked', true);
        } else {
            //split
            var mode = result.Model.split(",")

            if (mode[0] == 2) {
                rdPlayer.prop('checked', true);
            } else if (mode[0] == 3) {
                rdTie.prop('checked', true);
            } else if (mode[0] == 4) {
                rdBanker.prop('checked', true);
            } else {
                rdRandom.prop('checked', true);
            }

            if (mode[1] && mode[1] == 1)
                rdPlayerPair.prop('checked', true);
            else if (mode[1] && mode[1] == 5)
                rdBankerPair.prop('checked', true);
            else
                rdRandomPair.prop('checked', true);
        }

        txtSessionID.html("#" + result.SessionID);
        txtTimeCountdown.html(result.Ellapsed);
        txtCurrentState.html(result.CurrentState);

        if (result.CurrentState == "Betting" || result.CurrentState == "ShowResult") {

            var BetSide_5 = "";
            var BetSide_4 = "";
            var BetSide_3 = "";
            var BetSide_2 = "";
            var BetSide_1 = "";

            var total_5 = 0;
            var total_4 = 0;
            var total_3 = 0;
            var total_2 = 0;
            var total_1 = 0;

            var totalBet_5 = 0;
            var totalBet_4 = 0;
            var totalBet_3 = 0;
            var totalBet_2 = 0;
            var totalBet_1 = 0;

            for (let i = 0; i < result.BetBalancesPlayerPair.length; i++) {
                var BetSide = "<tr><td>" + i + "</td><td>" + result.BetBalancesPlayerPair[i].AccountID + "</td><td>" + result.BetBalancesPlayerPair[i].Nickname + "</td><td>" + formatMoney(result.BetBalancesPlayerPair[i].BetValue) + "</td></tr>";
                BetSide_1 += BetSide;
                totalBet_1 += result.TotalPlayerPair;
                total_1 += result.BetBalancesPlayerPair[i].BetValue;
            }

            for (let i = 0; i < result.BetBalancesPlayer.length; i++) {
                var BetSide = "<tr><td>" + i + "</td><td>" + result.BetBalancesPlayer[i].AccountID + "</td><td>" + result.BetBalancesPlayer[i].Nickname + "</td><td>" + formatMoney(result.BetBalancesPlayer[i].BetValue) + "</td></tr>";
                BetSide_2 += BetSide;
                totalBet_2 += result.TotalPlayer;
                total_2 += result.BetBalancesPlayer[i].BetValue;
            }

            for (let i = 0; i < result.BetBalancesTie.length; i++) {
                var BetSide = "<tr><td>" + i + "</td><td>" + result.BetBalancesTie[i].AccountID + "</td><td>" + result.BetBalancesTie[i].Nickname + "</td><td>" + formatMoney(result.BetBalancesTie[i].BetValue) + "</td></tr>";
                BetSide_3 += BetSide;
                totalBet_3 += result.TotalTie;
                total_3 += result.BetBalancesTie[i].BetValue;
            }

            for (let i = 0; i < result.BetBalancesBanker.length; i++) {
                var BetSide = "<tr><td>" + i + "</td><td>" + result.BetBalancesBanker[i].AccountID + "</td><td>" + result.BetBalancesBanker[i].Nickname + "</td><td>" + formatMoney(result.BetBalancesBanker[i].BetValue) + "</td></tr>";
                BetSide_4 += BetSide;
                totalBet_4 += result.TotalBanker;
                total_4 += result.BetBalancesBanker[i].BetValue;
            }

            for (let i = 0; i < result.BetBalancesBankerPair.length; i++) {
                var BetSide = "<tr><td>" + i + "</td><td>" + result.BetBalancesBankerPair[i].AccountID + "</td><td>" + result.BetBalancesBankerPair[i].Nickname + "</td><td>" + formatMoney(result.BetBalancesBankerPair[i].BetValue) + "</td></tr>";
                BetSide_5 += BetSide;
                totalBet_5 += result.TotalBankerPair;
                total_5 += result.BetBalancesBankerPair[i].BetValue;
            }

            if (BetSide_5.length == 0) {
                BetSide_5 = "<tr><th cospan='3'>Không có người chơi</th></tr>";
            }
            if (BetSide_4.length == 0) {
                BetSide_4 = "<tr><th cospan='3'>Không có người chơi</th></tr>";
            }
            if (BetSide_3.length == 0) {
                BetSide_3 = "<tr><th cospan='3'>Không có người chơi</th></tr>";
            }
            if (BetSide_2.length == 0) {
                BetSide_2 = "<tr><th cospan='3'>Không có người chơi</th></tr>";
            }
            if (BetSide_1.length == 0) {
                BetSide_1 = "<tr><th cospan='3'>Không có người chơi</th></tr>";
            }

            txtTotal_1.html(formatMoney(total_1));
            txtTotal_2.html(formatMoney(total_2));
            txtTotal_3.html(formatMoney(total_3));
            txtTotal_4.html(formatMoney(total_4));
            txtTotal_5.html(formatMoney(total_5));

            txtTotalBet_1.html(formatMoney(totalBet_1));
            txtTotalBet_2.html(formatMoney(totalBet_2));
            txtTotalBet_3.html(formatMoney(totalBet_3));
            txtTotalBet_4.html(formatMoney(totalBet_4));
            txtTotalBet_5.html(formatMoney(totalBet_5));

            txtBetSide_1.html(BetSide_1);
            txtBetSide_2.html(BetSide_2);
            txtBetSide_3.html(BetSide_3);
            txtBetSide_4.html(BetSide_4);
            txtBetSide_5.html(BetSide_5);
        }
    };


    $(document).ready(function () {
        GetSession(true);
        setInterval(GetSession, 999);
    })</script>