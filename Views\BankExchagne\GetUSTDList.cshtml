﻿@using MsWebGame.CSKH.Database.DTO
@using TraditionGame.Utilities.Utils
@model List<UserBankRequest>
@{ 
    var status =(List<SelectListItem>) ViewBag.GetStatus;
}
<link href="~/Content/label.css" rel="stylesheet" />
<style>
    .adminContent tr:nth-child(even) {
        background-color: #e4cfcf;
    }

    .adminContent td {
        width: 1px;
        white-space: nowrap;
    }
</style>
<div class="t-widget t-grid" id="userCardSwap-grid">
    <table cellspacing="0">
        <thead class="t-grid-header">
            <tr>
                <th class="t-header" scope="col" ><span class="t-link">&nbsp; </span></th>
                <th class="t-header" scope="col" colspan="2"><span class="t-link">Trạng thái </span></th>
                <th class="t-header" scope="col" rowspan="2"><span class="t-link">Request ID</span></th>
                <th class="t-header" scope="col" rowspan="2"><span class="t-link" >Loại giao dịch</span></th>               
                <th class="t-header" scope="col" rowspan="2"><span class="t-link">Mã giao dịch</span></th>
                <th class="t-header" scope="col" rowspan="2"><span class="t-link">Nickname</span></th>
                <th class="t-header" scope="col" rowspan="2"><span class="t-link">Ngày tạo giao dịch</span></th>
                <th class="t-header" scope="col" rowspan="2"><span class="t-link" >Tỉ giá game</span></th>
                <th class="t-header" scope="col" rowspan="2"><span class="t-link">Tỉ giá (N/R) USDT</span></th>
                <th class="t-header" scope="col" colspan="3"><span class="t-link">Yêu cầu của khách hàng</span></th>                
                <th class="t-header" scope="col" colspan="3"><span class="t-link">USDT Phản hồi</span></th>            
                <th class="t-header" scope="col"><span class="t-link">&nbsp; </span></th>
               
                </tr>
            <tr>
                
                <th class="t-header" scope="col"><span class="t-link">Cổng </span></th>
                <th class="t-header" scope="col"><span class="t-link">Game </span></th>
                <th class="t-header" scope="col"><span class="t-link">Đối tác</span></th>                                
                <th class="t-header" scope="col"><span class="t-link">Tiền Game</span></th>                
                <th class="t-header" scope="col"><span class="t-link">Tiền VND </span></th>
                <th class="t-header" scope="col"><span class="t-link">Tiền USDT</span></th>                                
                <th class="t-header" scope="col"><span class="t-link">Tiền VND(khách nhận) </span></th>
                <th class="t-header" scope="col"><span class="t-link">Tiền VND(cả phí truyển)</span></th>
                <th class="t-header" scope="col"><span class="t-link">Tiền USDT(khách nhận) </span></th>
                <th class="t-header" scope="col"><span class="t-link">&nbsp; </span></th>
                
                
            </tr>
        </thead>
        <tbody>
            @if (Model != null && Model.Any())
            {
                foreach (var item in Model)
                {                                                           
                    <tr>
                        <td>@item.ServieName</td>
                        <td>
                            <span class="@item.ColorCss"> @item.PartnerStatus</span> 
						</td>
                       
                        <td>@item.RequestID</td>
                        <td>@if (item.RequestType == 1)
                        {
                            @:Nạp tiền 
                        }
                            @if (item.RequestType == 2)
                            {
                               @:Rút tiền
                        }
                        </td>
                        
                        <td>@item.RequestCode</td>

                        <td>@item.DisplayName</td>
                        <td>@item.RequestDate</td>
                        <td>
                            
                               @item.Rate
      
                           
                        </td>
                        <td>
                            @item.ExchangeRate.DoubleMoneyFormat()
                        </td>
                        <td>
                            @if (item.RequestType == 1)
                            {
                                @item.ReceivedMoney.Value.LongToMoneyFormat()
                            }
                            else
                            {
                                @item.Amount.LongToMoneyFormat()
                            }
                       
                        </td>
                       
                        <td>
                            @if (item.RequestType == 1)
                            {
                                @item.Amount.LongToMoneyFormat()
                            }
                            else
                            {
                                @item.ReceivedMoney.LongToMoneyFormat()
                            }

                        </td>
                        <td>
                            @item.USDTAmount
                        </td>
                        <td>
                            @item.RealReceivedMoney.LongToMoneyFormat()
                        </td>
                        <td>
                            @item.RealAmount.DoubleMoneyFormat()
                        </td>
                        
                        <td>
                            @item.RealUSDTAmount
                        </td>
                       
                         <td class="t-last" style="width: 200px; text-align: center;">
                            @if (item.Status == 0)
                            {
                                <a href="javascript:GlobalHeader.userUSDTExamine(@item.RequestID, @item.UserID, 1);" class="t-button">Đồng ý</a>
                                <a href="javascript:GlobalHeader.userUSDTExamine(@item.RequestID, @item.UserID, 4);" class="t-button">Không đồng ý</a>
                            }
                        </td>
                       
                        
                    </tr>

                }
            }
        </tbody>
    </table>



    <div class="t-grid-pager t-grid-bottom">
        <div class="t-pager t-reset">
            <a class="t-link t-state-disabled" href="javascript:GlobalHeader.getUSDTListBankExchagne(1);">
                <span class="t-icon t-arrow-first">first</span>
            </a>
            <a class="t-link t-state-disabled" href="javascript:GlobalHeader.getUSDTListBankExchagne(@ViewBag.Prev);">
                <span class="t-icon t-arrow-prev">prev</span>
            </a>
            <div class="t-numeric">

                @for (int i = ViewBag.Start; i <= ViewBag.End; i++)
                {
                    if (i == ViewBag.CurrentPage)
                    {
                        <span class="t-state-active">@(i)</span>
                    }
                    else
                    {
                        <a href="javascript:GlobalHeader.getUSDTListBankExchagne(@(i));" class="t-link">@(i)</a>
                    }
                }
            </div>
            <a class="t-link" href="javascript:GlobalHeader.getUSDTListBankExchagne(@ViewBag.Next);">
                <span class="t-icon t-arrow-next">next</span>
            </a>
            <a class="t-link" href="javascript:GlobalHeader.getUSDTListBankExchagn(@ViewBag.TotalPage);">
                <span class="t-icon t-arrow-last">last</span>
            </a>
        </div>
        <div class="t-status-text">Displaying items @ViewBag.StartIndex - @ViewBag.EndIndex of @ViewBag.TotalRecord</div>
    </div>

</div>