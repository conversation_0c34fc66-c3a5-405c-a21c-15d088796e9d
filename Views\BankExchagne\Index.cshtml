﻿@{
    ViewBag.Title = "Lịch sử đổi thẻ";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@using MsWebGame.CSKH.Database.DTO
@using Telerik.Web.Mvc.UI
@using MsWebGame.CSKH.Utils
@functions{
    private bool IsDisplayFunction(string UserRoles)
    {
        string RoleCode = Session["RoleCode"].ToString();
        if (UserRoles != "*")
        {
            var arrRoles = UserRoles.Split(',');
            var curRoles = RoleCode.Split(',');
            var listCommon = arrRoles.Intersect(curRoles).ToList();
            if (listCommon != null && listCommon.Any())
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        return true;
    }
    private bool IsDisplayMenuByUserName(string UserRoles)
    {
        string RoleCode = Session["UserName"] != null ? Session["UserName"].ToString() : string.Empty;
        if (UserRoles != "*")
        {
            var arrRoles = UserRoles.Split(',');
            var curRoles = RoleCode.Split(',');
            var listCommon = arrRoles.Intersect(curRoles).ToList();
            if (listCommon != null && listCommon.Any())
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        return true;
    }
}
<div class="section-header">
    <div class="title">
        <img src="@Url.Content("~/Content/images/ico-configuration.png")" alt="" />
        Lịch sử giao dịch USDT
    </div>
</div>
<div style="float: left; width: 50%;">
    <table style="width: 100%;">
        <tbody>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    <label>Chọn cổng</label>
                </td>
                <td class="adminData">
                    @Html.DropDownList("serviceId", new SelectList(ViewBag.ServiceBox, "Value", "Text"), "--Chọn cổng--", new { @class = "text-box single-line" })
                </td>
            </tr>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    <label>Loại Giao Dịch</label>
                </td>
                <td class="adminData">
                    @Html.DropDownList("Type", new SelectList(ViewBag.BankTypes, "Value", "Text"),"--Chọn loại giao dịch--", new { @class = "text-box single-line" })
                </td>
            </tr>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    <label>Loại Giao Dịch</label>
                </td>
                <td class="adminData">
                    @Html.DropDownList("status", new SelectList(ViewBag.GetStatus, "Value", "Text"), "--Chọn trạng thái--", new { @class = "text-box single-line" })
                </td>
            </tr>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    <label>Mã Request</label>
                </td>
                <td class="adminData">
                    <input class="text-box single-line" id="code" type="text">
                    <span class="field-validation-valid" data-valmsg-for="code"></span>
                </td>
            </tr>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    <label>NickName</label>
                </td>
                <td class="adminData">
                    <input class="text-box single-line" id="nickName" type="text" value="@ViewBag.NickName">
                    <span class="field-validation-valid" data-valmsg-for="nickName"></span>
                </td>
            </tr>


            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    <label>Từ Ngày</label>
                </td>
                <td class="adminData">
                    @Html.Telerik().DatePicker().Name("fromDate")
                    <span class="field-validation-valid" data-valmsg-for="buyDate"></span>
                </td>
            </tr>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    <label>Tới ngày</label>
                </td>
                <td class="adminData">
                    @Html.Telerik().DatePicker().Name("toDate")
                    <span class="field-validation-valid" data-valmsg-for="buyDate"></span>
                </td>
            </tr>

            <tr>
                <td class="adminTitle">
                    &nbsp;
                </td>
                <td>
                    <input type="button" onclick="GlobalHeader.getUSDTListBankExchagne(1)" id="btnSearch" class="t-button" value="@AppConstants.CONFIG.SEARCH" style="margin-bottom: 6px;">
                </td>
            </tr>
        </tbody>
    </table>
    </div>
 @if (IsDisplayFunction("ADMIN")|| IsDisplayMenuByUserName("admin_test,cskh_01,cskh_09"))
 {
<div style="float: right; width: 50%;">
    <table>
        <tbody>
            <tr>
                <td colspan="2" style="color:red"><b>Chỉ hoàn tiền _Sau khi lên trang quản trị đối tác tra cứu</b></td>
            </tr>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    <label>Mã Request</label>
                </td>
                <td class="adminData">
                    <input type="text" id="Check_RequestID" class="text-box single-line" />
                    <input type="button" id="btnCheck" class="t-button" value="Kiểm tra" onclick="javascript: GlobalHeader.GetUSDTExchangeByID();">
                </td>
            </tr>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    <label>Tên hiển thị</label>
                </td>
                <td class="adminData">
                    <input type="text" id="Check_NickName" class="text-box single-line" readonly disabled />
                </td>
            </tr>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    <label>Loại giao dịch</label>
                </td>
                <td class="adminData">
                    <input type="text" id="Check_Type" class="text-box single-line" readonly disabled />
                </td>
            </tr>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    <label>Mã code</label>
                </td>
                <td class="adminData">
                    <input type="text" id="Check_Code" class="text-box single-line" readonly disabled />
                </td>
            </tr>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    <label>Trạng thái</label>
                </td>
                <td class="adminData">
                    <input type="text" id="Check_Status" class="text-box single-line" readonly disabled />
                </td>
            </tr>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    <label>Tiền Game Yêu cầu</label>
                </td>
                <td class="adminData">
                    <input type="text" id="Check_Game_Request" class="text-box single-line" readonly disabled />
                </td>
            </tr>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    <label>Tiền USDT Yêu cầu</label>
                </td>
                <td class="adminData">
                    <input type="text" id="Check_USDT_Request" class="text-box single-line" readonly disabled />
                 <input type="button" id="btnCheck" class="t-button" value="Tính toán" onclick="javascript: GlobalHeader.CalcuatelUSDTExchagne();">
                </td>
            </tr>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    <label>Tiền VND Yêu cầu</label>
                </td>
                <td class="adminData">
                    <input type="text" id="Check_VND_Request" class="text-box single-line" readonly disabled />
                </td>
            </tr>
          
           
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    <label>Mô tả</label>
                </td>
                <td class="adminData">

                    <input type="text" id="Check_Note" class="text-box single-line" />
                </td>
            </tr>
            
           
            <tr>
                <td class="adminTitle"></td>
                <td class="adminData">
                    <input type="button" id="GiftcodeProgress" class="t-button" value="Giao dịch thành công" onclick="javascript: GlobalHeader.USDTCheckExchagne(1);">
                    <input type="button" id="GiftcodeProgress" class="t-button" value="Giao dịch thất bại(Hoàn lại tiền cho khách)" onclick="javascript: GlobalHeader.USDTCheckExchagne(0);">

                </td>
            </tr>
        </tbody>
    </table>
    <div id="bindGiftcodeProgress">
    </div>
</div>
        }
    <table class="adminContent">
        <tbody>
            <tr>
                <td>
                    <div id="hisCardExchange">
                        @Html.Action("GetUSTDList", "BankExchagne")
                    </div>
                </td>
            </tr>
        </tbody>
    </table>



