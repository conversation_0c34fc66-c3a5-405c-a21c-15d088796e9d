﻿@model MsWebGame.CSKH.Models.BankSecondary.BankOperatorsSecondaryModel
@using MsWebGame.CSKH.Utils
<div class="t-content t-state-active" style="display: block;">
    <table class="adminContent customer-info-tab">
        <tr>
            <td class="adminTitle">
                <img src="~/Content/images/ico-help.gif">
                <label>Chọn cổng</label>
            </td>
            <td class="adminData">
                @Html.DropDownListFor(m => m.serviceId, new SelectList(ViewBag.ServiceBox, "Value", "Text"), new { @class = "text-box single-line" })
            </td>
        </tr>
        <tr>
            <td class="adminTitle">
                <label>@Html.LabelFor(m => m.OperatorName)  <span style="color:red;">*</span></label>
            </td>
            <td class="adminData">
                @Html.TextBoxFor(m => m.OperatorName, new { @class = "text-box single-line", placeholder = "Tên ngân hàng" })
                @Html.ValidationMessageFor(m => m.OperatorName)
            </td>
        </tr>
        <tr>
            <td class="adminTitle">
                <label>@Html.LabelFor(m => m.OperatorCode) <span style="color:red;">*</span></label>
            </td>
            <td class="adminData">
                
                    @Html.TextBoxFor(m => m.OperatorCode, new { @class = "text-box single-line", placeholder = "Mã ngân hàng" })
                
                @Html.ValidationMessageFor(m => m.OperatorCode)
            </td>
        </tr>
        <tr>
            <td class="adminTitle">
                <label>@Html.LabelFor(m => m.Rate) <span style="color:red;">*</span></label>
            </td>
            <td class="adminData">
                @Html.TextBoxFor(m => m.Rate, new { @class = "text-box single-line", @placeholder = "VD:0.8,0.25,1,1.25", @onKeypress = " GlobalHeader.keyPressDoubleNumber(this,event); ", @onkeyup = "GlobalHeader.keyupDouble(this,event);" })
                @Html.ValidationMessageFor(m => m.Rate)
            </td>
        </tr>
        <tr>
            <td class="adminTitle">
                <label>@Html.LabelFor(m => m.Status)  <span style="color:red;">*</span></label>
            </td>
            <td class="adminData">
                @Html.DropDownListFor(m => m.Status, new List<SelectListItem>()
                {
                    new SelectListItem() {Value = "True", Text = "Hoạt động"},
                    new SelectListItem() {Value = "False", Text = "Tạm dừng"},
                }, new { @class = "text-box single-line" })
                @Html.ValidationMessageFor(m => m.Status)
                <span class="field-validation-valid" data-valmsg-for="limitQuota"></span>
            </td>
        </tr>
        <tr>
            <td class="adminTitle"></td>
            <td class="adminData">
                <input type="submit" onclick="return GlobalHeader.validationSetGiftcode(0);" name="save" class="t-button" value="@AppConstants.CONFIG.SAVE" />
            </td>
        </tr>
    </table>
</div>
