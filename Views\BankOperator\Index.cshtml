﻿@{
    ViewBag.Title = "Cấu hình USDT";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@using MsWebGame.CSKH.Database.DTO
@using Telerik.Web.Mvc.UI
@using MsWebGame.CSKH.Utils

@using (Html.BeginForm())
{
    <div class="section-header">
        <div class="title">
            <img src="@Url.Content("~/Content/images/ico-configuration.png")" alt="" />
            Cấu hình 
        </div>
    </div>
    <table style="width: 100%;">
        <tbody>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    <label>Chọn cổng</label>
                </td>
                <td class="adminData">
                    @Html.DropDownList("serviceId", new SelectList(ViewBag.ServiceBox, "Value", "Text"),"--<PERSON><PERSON><PERSON> cổng--", new { @class = "text-box single-line" })
                </td>
            </tr>
            
            <tr>
                <td class="adminTitle"></td>
                <td class="adminData">
                    <input type="button" id="btnSearch" class="t-button" value="@AppConstants.CONFIG.SEARCH">
                </td>
            </tr>
        </tbody>
    </table>
    <h2>
        Danh sách
    </h2>
    <table class="adminContent">
        <tr>
            <td>
                @(Html.Telerik().Grid<BankOperators>()
                    .Name("settings-grid")
                    .DataKeys(x => { x.Add(y => y.ID); })
                    .Columns(columns =>
                    {

                        columns.Bound(x => x.OperatorCode).Title("Mã code");
                        columns.Bound(x => x.OperatorName).Title("Tên đối tác");
                        columns.Bound(x => x.Rate).HtmlAttributes(new { @class = "text-right" }).Title("Tỉ lệ nạp");
                        columns.Bound(x => x.ExchangeRate).HtmlAttributes(new { @class = "text-right" }).Title("Tỉ lệ rút");
                        columns.Bound(x => x.Status).Title("Trạng thái nạp").ClientTemplate("<input type=\"checkbox\"  <#= Status?\"checked\":\"\" #> \">"); ;
                        columns.Bound(x => x.ExchangeStatus).Title("Trạng thái rút").ClientTemplate("<input type=\"checkbox\"  <#= ExchangeStatus?\"checked\":\"\" #> \">"); ;
                        columns.Command(commands =>
                        {
                            commands.Edit().Text(AppConstants.CONFIG.EDIT).HtmlAttributes(new { @class = "text-center" });
                        }).Width(200);
                    })
                    .Editable(x => { x.Mode(GridEditMode.InLine); })
                    .Pageable(settings => settings.PageSize(AppConstants.CONFIG.GRID_SIZE).Position(GridPagerPosition.Both))
                    .DataBinding(dataBinding =>
                    {
                        dataBinding.Ajax().Select("GetList", "BankOperator").Update("Update", "BankOperator");
                    })
                    .ClientEvents(x => x.OnError("grid_onError").OnEdit("grid_onEdit").OnDataBinding("onDataBinding"))
                    .EnableCustomBinding(true))

                <script type="text/javascript">
                    function grid_onError(e) {
                        alert(e.XMLHttpRequest.responseText);
                        e.preventDefault();
                    }

                    function grid_onEdit(e) {
                        $(e.form).find('#OperatorCode').attr('readonly', true);
                        
                    }

                    function onDataBinding(e) {
                        var searchModel = {
                            serviceId: $('#serviceId').val().trim(),
                            
                        };
                        e.data = searchModel;
                    }

                    $(document).ready(function () {
                        $('#btnSearch').click(function () {
                            var grid = $('#settings-grid').data('tGrid');
                            grid.currentPage = 1; //new search. Set page size to 1
                            grid.ajaxRequest();
                            return false;
                        });
                    });
                </script>
            </td>
        </tr>
    </table>
}

