@*
    Partial view để thêm vào menu navigation
    Sử dụng: @Html.Partial("_WithdrawalSettingsMenu")
*@

@{
    var currentController = ViewContext.RouteData.Values["controller"].ToString();
    var isActive = currentController.Equals("WithdrawalSettings", StringComparison.OrdinalIgnoreCase);
}

<li class="@(isActive ? "active" : "")">
    <a href="@Url.Action("Index", "WithdrawalSettings")" title="Quản lý cài đặt MIN/MAX rút tiền">
        <img src="@Url.Content("~/Content/images/ico-configuration.png")" alt="" />
        <span>Cài đặt rút tiền</span>
    </a>
    @if (isActive)
    {
        <ul class="submenu">
            <li class="@(ViewContext.RouteData.Values["action"].ToString().Equals("Index", StringComparison.OrdinalIgnoreCase) ? "active" : "")">
                <a href="@Url.Action("Index", "WithdrawalSettings")">Danh sách cài đặt</a>
            </li>
            <li class="@(ViewContext.RouteData.Values["action"].ToString().Equals("Create", StringComparison.OrdinalIgnoreCase) ? "active" : "")">
                <a href="@Url.Action("Create", "WithdrawalSettings")">Thêm mới cài đặt</a>
            </li>
        </ul>
    }
</li>

<style>
    .submenu {
        list-style: none;
        padding-left: 20px;
        margin: 5px 0;
    }
    
    .submenu li {
        margin: 2px 0;
    }
    
    .submenu li a {
        font-size: 12px;
        color: #666;
        text-decoration: none;
        padding: 2px 5px;
        display: block;
    }
    
    .submenu li a:hover,
    .submenu li.active a {
        color: #007cba;
        background-color: #f0f8ff;
    }
</style>
