@model MsWebGame.CSKH.Models.WithdrawalSettingsModel

@{
    ViewBag.Title = "Thêm mới cài đặt rút tiền";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@using (Html.BeginForm())
{
    <div class="section-header">
        <div class="title">
            <img src="@Url.Content("~/Content/images/ico-configuration.png")" alt="" />
            Thêm mới cài đặt MIN/MAX rút tiền
        </div>
        <div class="options">
            <a href="@Url.Action("Index", "WithdrawalSettings")" class="t-button">Quay lại danh sách</a>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(ViewBag.Message as string))
    {
        <div class="message-box @(ViewBag.MessageType == "success" ? "success" : "error")">
            @ViewBag.Message
        </div>
    }

    <div class="form-container">
        <table class="adminContent">
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    @Html.LabelFor(m => m.SettingName, new { @class = "required" })
                </td>
                <td class="adminData">
                    @Html.TextBoxFor(m => m.SettingName, new { @class = "text-box single-line", maxlength = "100" })
                    @Html.ValidationMessageFor(m => m.SettingName)
                </td>
            </tr>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    @Html.LabelFor(m => m.Description, new { @class = "required" })
                </td>
                <td class="adminData">
                    @Html.TextAreaFor(m => m.Description, new { @class = "text-box multi-line", rows = "3", maxlength = "500" })
                    @Html.ValidationMessageFor(m => m.Description)
                </td>
            </tr>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    @Html.LabelFor(m => m.SettingType, new { @class = "required" })
                </td>
                <td class="adminData">
                    @Html.DropDownListFor(m => m.SettingType, (List<SelectListItem>)ViewBag.SettingTypes, new { @class = "text-box single-line" })
                    @Html.ValidationMessageFor(m => m.SettingType)
                </td>
            </tr>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    @Html.LabelFor(m => m.ApplyFor)
                </td>
                <td class="adminData">
                    @Html.DropDownListFor(m => m.ApplyFor, (List<SelectListItem>)ViewBag.ApplyForOptions, new { @class = "text-box single-line" })
                    @Html.ValidationMessageFor(m => m.ApplyFor)
                </td>
            </tr>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    @Html.LabelFor(m => m.MinAmount, new { @class = "required" })
                </td>
                <td class="adminData">
                    @Html.TextBoxFor(m => m.MinAmount, new { @class = "text-box single-line number-input", placeholder = "Nhập số tiền tối thiểu" })
                    @Html.ValidationMessageFor(m => m.MinAmount)
                    <div class="field-hint">Đơn vị: VND</div>
                </td>
            </tr>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    @Html.LabelFor(m => m.MaxAmount, new { @class = "required" })
                </td>
                <td class="adminData">
                    @Html.TextBoxFor(m => m.MaxAmount, new { @class = "text-box single-line number-input", placeholder = "Nhập số tiền tối đa" })
                    @Html.ValidationMessageFor(m => m.MaxAmount)
                    <div class="field-hint">Đơn vị: VND</div>
                </td>
            </tr>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    @Html.LabelFor(m => m.Note)
                </td>
                <td class="adminData">
                    @Html.TextAreaFor(m => m.Note, new { @class = "text-box multi-line", rows = "3", maxlength = "1000" })
                    @Html.ValidationMessageFor(m => m.Note)
                </td>
            </tr>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    @Html.LabelFor(m => m.IsActive)
                </td>
                <td class="adminData">
                    @Html.CheckBoxFor(m => m.IsActive)
                    @Html.ValidationMessageFor(m => m.IsActive)
                </td>
            </tr>
            <tr>
                <td class="adminTitle"></td>
                <td class="adminData">
                    <input type="submit" class="t-button" value="Thêm mới" />
                    <input type="button" class="t-button" value="Làm mới" onclick="resetForm()" />
                </td>
            </tr>
        </table>
    </div>
}

<script src="@Url.Content("~/Scripts/withdrawal-settings.js")" type="text/javascript"></script>

<style>
    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding: 10px;
        background-color: #f5f5f5;
        border: 1px solid #ddd;
    }
    
    .section-header .title {
        font-size: 16px;
        font-weight: bold;
        display: flex;
        align-items: center;
    }
    
    .section-header .title img {
        margin-right: 8px;
    }
    
    .section-header .options a {
        background-color: #6c757d;
        color: white;
        padding: 8px 16px;
        text-decoration: none;
        border-radius: 4px;
    }
    
    .section-header .options a:hover {
        background-color: #545b62;
    }
    
    .form-container {
        background-color: white;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    
    .message-box {
        padding: 10px;
        margin-bottom: 20px;
        border-radius: 4px;
    }
    
    .message-box.success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .message-box.error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .required:after {
        content: " *";
        color: red;
    }
    
    .field-hint {
        font-size: 12px;
        color: #666;
        margin-top: 5px;
    }
    
    .input-validation-error {
        border-color: #dc3545 !important;
        background-color: #fff5f5;
    }
    
    .field-validation-error {
        color: #dc3545;
        font-size: 12px;
        display: block;
        margin-top: 5px;
    }
    
    .number-input {
        text-align: right;
    }
    
    .multi-line {
        resize: vertical;
        min-height: 60px;
    }
</style>
