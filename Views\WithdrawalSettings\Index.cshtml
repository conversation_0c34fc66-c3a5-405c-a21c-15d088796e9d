@{
    ViewBag.Title = "Quản lý cài đặt MIN/MAX rút tiền";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@using MsWebGame.CSKH.Models
@using Telerik.Web.Mvc.UI
@using MsWebGame.CSKH.Utils

@using (Html.BeginForm())
{
    <div class="section-header">
        <div class="title">
            <img src="@Url.Content("~/Content/images/ico-configuration.png")" alt="" />
            Quản lý cài đặt MIN/MAX rút tiền
        </div>
        <div class="options">
            <a href="@Url.Action("Create", "WithdrawalSettings")" class="t-button">Thêm mới cài đặt</a>
        </div>
    </div>

    <table style="width: 100%;">
        <tbody>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    <label>Loại cài đặt</label>
                </td>
                <td class="adminData">
                    @Html.DropDownList("SettingType", (List<SelectListItem>)ViewBag.SettingTypes, new { @class = "text-box single-line", id = "settingType" })
                </td>
            </tr>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    <label>Tên cài đặt</label>
                </td>
                <td class="adminData">
                    <input class="text-box single-line" id="settingName" type="text" placeholder="Nhập tên cài đặt">
                </td>
            </tr>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    <label>Áp dụng cho</label>
                </td>
                <td class="adminData">
                    @Html.DropDownList("ApplyFor", (List<SelectListItem>)ViewBag.ApplyForOptions, new { @class = "text-box single-line", id = "applyFor" })
                </td>
            </tr>
            <tr>
                <td class="adminTitle">
                    <img src="~/Content/images/ico-help.gif">
                    <label>Trạng thái</label>
                </td>
                <td class="adminData">
                    <select id="isActive" class="text-box single-line">
                        <option value="">Tất cả</option>
                        <option value="true">Hoạt động</option>
                        <option value="false">Tạm dừng</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td class="adminTitle"></td>
                <td class="adminData">
                    <input type="button" id="btnSearch" class="t-button" value="@AppConstants.CONFIG.SEARCH">
                    <input type="button" id="btnReset" class="t-button" value="Làm mới">
                </td>
            </tr>
        </tbody>
    </table>

    <h2>Danh sách cài đặt rút tiền</h2>
    
    <table class="adminContent">
        <tr>
            <td>
                @(Html.Telerik().Grid<WithdrawalSettingsDTO>()
                    .Name("withdrawal-settings-grid")
                    .DataKeys(x => { x.Add(y => y.ID); })
                    .Columns(columns =>
                    {
                        columns.Bound(x => x.SettingName).Title("Tên cài đặt").Width(200);
                        columns.Bound(x => x.SettingTypeText).Title("Loại cài đặt").Width(150);
                        columns.Bound(x => x.ApplyForText).Title("Áp dụng cho").Width(120);
                        columns.Bound(x => x.MinAmountFormatted).Title("Giá trị tối thiểu").Width(150).HtmlAttributes(new { @class = "text-right" });
                        columns.Bound(x => x.MaxAmountFormatted).Title("Giá trị tối đa").Width(150).HtmlAttributes(new { @class = "text-right" });
                        columns.Bound(x => x.StatusText).Title("Trạng thái").Width(100).HtmlAttributes(new { @class = "text-center" });
                        columns.Bound(x => x.CreatedDate).Title("Ngày tạo").Width(120).Format("{0:dd/MM/yyyy}");
                        columns.Command(commands =>
                        {
                            commands.Custom("Edit").Text("Sửa").Action("Edit", "WithdrawalSettings").HtmlAttributes(new { @class = "t-button t-button-icon t-edit" });
                            commands.Custom("Delete").Text("Xóa").HtmlAttributes(new { @class = "t-button t-button-icon t-delete", onclick = "deleteWithdrawalSetting(this)" });
                        }).Title("Thao tác").Width(120).HtmlAttributes(new { @class = "text-center" });
                    })
                    .Pageable(settings => settings.PageSize(20).Position(GridPagerPosition.Both))
                    .DataBinding(dataBinding =>
                    {
                        dataBinding.Ajax().Select("GetWithdrawalSettings", "WithdrawalSettings");
                    })
                    .ClientEvents(x => x.OnError("grid_onError").OnDataBinding("onDataBinding"))
                    .EnableCustomBinding(true))

                <script src="@Url.Content("~/Scripts/withdrawal-settings.js")" type="text/javascript"></script>
                <script type="text/javascript">
                    function grid_onError(e) {
                        alert('Có lỗi xảy ra: ' + e.XMLHttpRequest.responseText);
                        e.preventDefault();
                    }

                    function onDataBinding(e) {
                        var searchModel = {
                            settingType: $('#settingType').val(),
                            settingName: $('#settingName').val().trim(),
                            applyFor: $('#applyFor').val(),
                            isActive: $('#isActive').val() === '' ? null : $('#isActive').val() === 'true'
                        };
                        e.data = searchModel;
                    }
                </script>
            </td>
        </tr>
    </table>
}

<style>
    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding: 10px;
        background-color: #f5f5f5;
        border: 1px solid #ddd;
    }
    
    .section-header .title {
        font-size: 16px;
        font-weight: bold;
        display: flex;
        align-items: center;
    }
    
    .section-header .title img {
        margin-right: 8px;
    }
    
    .section-header .options a {
        background-color: #007cba;
        color: white;
        padding: 8px 16px;
        text-decoration: none;
        border-radius: 4px;
    }
    
    .section-header .options a:hover {
        background-color: #005a87;
    }
    
    .text-right {
        text-align: right !important;
    }
    
    .text-center {
        text-align: center !important;
    }
    
    .t-button-icon {
        margin-right: 5px;
    }
    
    .t-edit {
        background-color: #28a745;
        color: white;
    }
    
    .t-delete {
        background-color: #dc3545;
        color: white;
    }
</style>
